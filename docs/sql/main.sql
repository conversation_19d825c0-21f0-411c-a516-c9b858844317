-- 活动
CREATE TABLE `exch_activity`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `activity_id`  varchar(32)   NOT NULL DEFAULT 0 COMMENT '活动id',
    `type`         tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动类型. 1. 积分商城 2. 兑换活动',
    `game_base_id` int(11) NOT NULL DEFAULT 0 COMMENT '游戏baseID',
    `game_name`    varchar(128)  NOT NULL DEFAULT '' COMMENT '游戏名称',
    `sdk_type`     int(11) NOT NULL DEFAULT 0 COMMENT 'sdk type',
    `game_id`      int(11) NOT NULL DEFAULT 0 COMMENT '游戏ID',
    `title`        varchar(30)   NOT NULL DEFAULT '' COMMENT '活动名称',
    `start_time`   int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间',
    `end_time`     int(11) NOT NULL DEFAULT 0 COMMENT '活动开始时间',
    `rule`         varchar(1000) NOT NULL DEFAULT '' COMMENT '兑换规则',
    `ext`          varchar(2048) NOT NULL DEFAULT '' COMMENT '扩展字段',
    `operator`     varchar(30)   NOT NULL DEFAULT '' COMMENT '提交人',
    `status`       varchar(10)   NOT NULL DEFAULT 'draft' COMMENT 'draft:草稿，published：已发布 stopped:已终止',
    `ctime`        datetime      NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`        datetime      NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp () COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_activity_id` (`activity_id`),
    KEY            `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='兑换活动基础配置表';

-- 兑换物配置表
CREATE TABLE `exch_activity_item`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `activity_id`       varchar(32)   NOT NULL DEFAULT '0' COMMENT '活动id',
    `show_start_time`   int(11) NOT NULL DEFAULT 0 COMMENT '展示开始时间',
    `show_end_time`     int(11) NOT NULL DEFAULT 0 COMMENT '展示结束时间',
    `exch_start_time`   int(11) NOT NULL DEFAULT 0 COMMENT '兑换开始时间',
    `exch_end_time`     int(11) NOT NULL DEFAULT 0 COMMENT '兑换结束时间',
    `item_name`         varchar(100)  NOT NULL DEFAULT '' COMMENT '兑换物名称',
    `item_img`          varchar(255)  NOT NULL DEFAULT '' COMMENT '兑换物图片',
    `item_desc`         varchar(255)  NOT NULL DEFAULT '' COMMENT '兑换物描述',
    `item_type`         int(11) NOT NULL DEFAULT '0' COMMENT '兑换物类型',
    `item_uniq_value`   varchar(64)   NOT NULL DEFAULT '' COMMENT '兑换物唯一标记,SPUD_ID/SKU_ID/PRODUCT_ID/券码',
    `price`             int(11) NOT NULL DEFAULT '0' COMMENT '兑换价值，积分数量OR道具数量',
    `stock_type`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '库存类型0:无限库存 1. 内置库存体系',
    `stock_id`          varchar(32)   NOT NULL DEFAULT '' COMMENT '库存标识ID',
    `total_stock`       int(11) NOT NULL DEFAULT 0 COMMENT '总库存',
    `times_limit_type`  varchar(20)   NOT NULL DEFAULT '' COMMENT '兑换次数限制类型.none:无限制 day: 每天限制 all: 活动期间限制 week: 每周限制',
    `times_limit_value` int(11) NOT NULL DEFAULT '0' COMMENT '兑换限制次数',
    `exch_limit_type`   varchar(20)   NOT NULL DEFAULT '' COMMENT '限制门槛类型 node：无限制 ip: IP等级限制 new: 新客限制',
    `exch_limit_value`  varchar(255)  NOT NULL DEFAULT '' COMMENT '限制门槛值 node: 空字符串 ip： ipId+level',
    `ext`               varchar(2048) NOT NULL DEFAULT '' COMMENT '扩展字段',
    `enable_time`       int(11) NOT NULL DEFAULT 0 COMMENT '第一次启用时间',
    `is_enable`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用 1：启用 0： 未启用',
    `ctime`             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `mtime`             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON
        UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY                 `ix_activity_id` (`activity_id`) USING BTREE COMMENT '活动id索引',
    KEY                 `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='兑换活动-兑换物表';

create table if not exists `exch_activity_i18n_content`
(
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    entity_id BIGINT NOT NULL COMMENT '实体ID（商品ID、分类ID等）',
    entity_type VARCHAR(32) NOT NULL COMMENT '实体类型：PRODUCT/CATEGORY/BRAND等',
    language VARCHAR(10) NOT NULL COMMENT '语言代码：zh/en/cn等',
    field_name VARCHAR(64) NOT NULL COMMENT '字段名：NAME/DESCRIPTION',
    field_value VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '字段值',

    -- 时间和操作信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建者ID',
    updated_by BIGINT COMMENT '更新者ID',
    -- 版本控制
    version INT DEFAULT 1 COMMENT '版本号',

    -- 索引设计
    UNIQUE KEY uk_entity_lang_field (entity_id, entity_type, language_code, field_name),
    KEY idx_entity_type_lang (entity_type, language_code),
    KEY idx_entity_id_type (entity_id, entity_type),
    KEY idx_field_name (field_name),
    KEY idx_language (language_code),
    KEY idx_active_priority (is_active, priority),
    KEY idx_created_at (created_at),
    KEY idx_updated_at (updated_at)
) COMMENT='通用多语言内容表' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
-- 兑换记录
CREATE TABLE `exch_record`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `serial_no`       varchar(32)  NOT NULL DEFAULT '' COMMENT '兑换流水号',
    `type`         tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动类型. 1. 积分商城 2. 兑换活动',
    `activity_id`     varchar(32)  NOT NULL DEFAULT 0 COMMENT '活动id',
    `uid`             bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
    `item_id`         int(11) NOT NULL DEFAULT 0 COMMENT '兑换物ID',
    `biz_id`          varchar(64)  NOT NULL DEFAULT '' COMMENT '幂等ID,同一个活动，同一个人,同一个幂等id只能对应一条记录',
    `item_title`      varchar(100) NOT NULL DEFAULT '' COMMENT '兑换物名称-兑换时的快照',
    `item_img`        varchar(255) NOT NULL DEFAULT '' COMMENT '兑换物图片-兑换时的快照',
    `item_desc`       varchar(255) NOT NULL DEFAULT '' COMMENT '兑换物描述-兑换时的快照',
    `item_type`       int(11) NOT NULL DEFAULT 0 COMMENT '兑换物类型',
    `item_uniq_value` varchar(64)  NOT NULL DEFAULT '' COMMENT '兑换物唯一标记,SPUD_ID/SKU_ID/券码',
    `price`           int(11) NOT NULL DEFAULT 0 COMMENT '兑换价值-兑换时的价值快照',
    `count`           int(11) NOT NULL DEFAULT 0 COMMENT '兑换数量',
    `ext`             varchar(512) NOT NULL DEFAULT 0 COMMENT '额外信息',
    `status`          tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 0: 兑换中 1: 兑换成功 2.已取消',
    `exchange_time`   int(11) NOT NULL DEFAULT 0 COMMENT '兑换时间',
    `ctime`           datetime     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`           datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp () COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_serial_no` (`serial_no`),
    UNIQUE KEY `uk_activity_id_uid_bz_id` (`activity_id`,`uid`,`biz_id`),
    KEY               `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='兑换记录表';

CREATE TABLE `exch_item_times_limit_record`
(
    `id`          bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `uid`         bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
    `activity_id` varchar(32) NOT NULL DEFAULT 0 COMMENT '活动id',
    `item_id`     int(11) NOT NULL DEFAULT 0 COMMENT '兑换物id（本地商品使用）',
    `item_uniq_value` varchar(64) NOT NULL DEFAULT '' COMMENT '商品唯一标识（远程商品使用）',
    `biz_date`    varchar(16) NOT NULL DEFAULT '' COMMENT '业务周期时间 天周月等',
    `times`       int(11) NOT NULL DEFAULT 0 COMMENT '兑换次数',
    `version`     int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
    `ctime`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `mtime`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uid_activity_id_item_uniq_value_biz_date` (`uid`, `activity_id`, `item_uniq_value`, `biz_date`),
    KEY `idx_uid_activity_id_item_id_biz_date` (`uid`, `activity_id`, `item_id`, `biz_date`),
    KEY           `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='兑换次数限制记录表';

CREATE TABLE `exch_item_times_limit_log`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `uid`             bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
    `limit_record_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '总表id',
    `exch_record_id`  varchar(32) NOT NULL DEFAULT '' COMMENT '兑换流水号',
    `exch_count`      int(11) NOT NULL DEFAULT 0 COMMENT '兑换数量',
    `record_version`  int(11) NOT NULL DEFAULT 0 COMMENT '兑换记录表版本号',
    `type`            tinyint     NOT NULL DEFAULT 1 COMMENT '类型 1: 扣减 2: 归还',
    `ctime`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `mtime`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_uid_limit_record_id_record_version` (`uid`, `limit_record_id`, `record_version`),
    KEY               `ix_uid_exch_record_id` (`uid`, `exch_record_id`),
    KEY               `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='兑换次数限制日志表';

-- 库存
CREATE TABLE `exch_stock`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `activity_id` varchar(32) NOT NULL DEFAULT 0 COMMENT '活动id',
    `stock_id`    varchar(32) NOT NULL DEFAULT '' COMMENT '对象id',
    `total_stock` int(11) NOT NULL DEFAULT 0 COMMENT '总库存',
    `stock`       int(11) NOT NULL DEFAULT 0 COMMENT '当前库存',
    `ctime`       datetime    NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`       datetime    NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp () COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stock_id` (`stock_id`),
    KEY           `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='库存表';

CREATE TABLE `exch_stock_log`
(
    `id`       bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `stock_id` varchar(32) NOT NULL DEFAULT '' COMMENT '对象id',
    `biz_id`   varchar(32) NOT NULL DEFAULT '' COMMENT '幂等id',
    `stock`    int(11) NOT NULL DEFAULT 0 COMMENT '执行库存',
    `uid`      bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
    `log_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1-扣减 2归还',
    `ctime`    datetime    NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`    datetime    NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp () COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_stock_id_biz_id_type` (`stock_id`, `biz_id`, `log_type`),
    KEY        `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='库存记录流水表';

CREATE TABLE `ec_task_log` (
                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
                               `bz_type` varchar(32) NOT NULL DEFAULT '' COMMENT '业务类型',
                               `bz_id` varchar(64) NOT NULL DEFAULT '' COMMENT '幂等ID',
                               `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 0: 待处理 1:处理成功 2. 处理失败',
                               `try_times` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
                               `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
                               `ctime` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
                               `mtime` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `uk_bz_type_bz_id` (`bz_type`,`bz_id`),
                               KEY `ix_mtime` (`mtime`) USING BTREE COMMENT '记录变更时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Eventual Consistency Task日志'