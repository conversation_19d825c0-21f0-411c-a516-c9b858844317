<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.biligame</groupId>
    <artifactId>game-activity-exchange</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>game-activity-exchange</name>
    <description>game-activity-exchange</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <pleiades.version>1.3.3</pleiades.version>
        <paladin.version>2.0.9</paladin.version>
        <spring.boot.version>2.7.18</spring.boot.version>
        <mybatis.springboot.version>2.3.2</mybatis.springboot.version>
        <mybatis.version>3.5.14</mybatis.version>
        <redisson.version>3.17.6.240709</redisson.version>
        <org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
        <warp.databus.version>1.2.0</warp.databus.version>
        <aspectj.version>1.9.6</aspectj.version>
        <sonar.exclusions>
            **/annotation/*,
            **/aop/*,
            **/config/*,
            **/config/**/*,
            **/dal/**,
            **/exception/**,
            **/interceptor/*,
            **/model/**,
            **/repository/*,
            **/resources/**/*,
        </sonar.exclusions>

    </properties>
    <dependencies>
        <!-- 项目依赖-->
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <!--            datasource-->
        <dependency>
            <groupId>pleiades.component.datasource</groupId>
            <artifactId>mysql</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <!-- paladin配置中心依赖-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>paladin-client</artifactId>
            <version>${paladin.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-client</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis.springboot.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-databus</artifactId>
            <version>${warp.databus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <!-- Hotspot JVM metrics-->
        <dependency>
            <groupId>pleiades.component.http</groupId>
            <artifactId>http-client</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component.http</groupId>
            <artifactId>http-server</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.middleware</groupId>
            <artifactId>identify</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>pleiades.middleware</groupId>-->
<!--            <artifactId>open</artifactId>-->
<!--            <version>${pleiades.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mapstruct</groupId>-->
<!--            <artifactId>mapstruct-processor</artifactId>-->
<!--            <version>${org.mapstruct.version}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjtools</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>6.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.hashids</groupId>
            <artifactId>hashids</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
            <version>2.7.7</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <outputDirectory>./target</outputDirectory>
                    <finalName>${project.name}</finalName>
                    <mainClass>com.biligame.activity.exchange.GameActivityExchangeApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <!--执行重新打包，将springboot依赖都打进最终的可执行jar包中-->
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 配置启动类 -->
            <plugin>
                <!-- 只在打包时排除文件,编译时不排除-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <excludes>
                        <!-- 本地会覆盖远程paladin配置-->
                        <exclude>application.yml</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <configuration>
                    <!--指定生成.exec文件的存放位置-->
                    <destFile>target/coverage-reports/jacoco-unit.exec</destFile>
                    <!--Jacoco是根据.exec文件生成最终的报告，所以需指定.exec的存放路径-->
                    <dataFile>target/coverage-reports/jacoco-unit.exec</dataFile>
                    <propertyName>surefireArgLine</propertyName>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-site</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <argLine>-Xmx256m -XX:PermSize=256m -XX:MaxPermSize=256m ${surefireArgLine}</argLine>
                    <skip>false</skip>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <disableXmlReport>false</disableXmlReport>
                    <printSummary>true</printSummary>
                    <enableAssertions>false</enableAssertions>
                    <useManifestOnlyJar>false</useManifestOnlyJar>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
