server:
  port: 8082
spring:
  application:
    name: activity-exchange-service
  # 国际化配置
  messages:
    basename: config/i18n/messages
    encoding: UTF-8
    cache-duration: 3600
app:
  master:
    cluster: db-cluster-name
    url: *************************************************************************************************************************************************************
    username: activity_exchange
    password: 2624QCmy1yV9SK5wwNZg9DZRP9S1WQ1l
    maxActive: 20
    minIdle: 1
    maxWait: 1000
    maxLifeTime: 1800000
    breaker:
      volumn: 100
      sleep: 1000
      errorRate: 50
      halfOpen: 10
# mybatis配置
mybatis:
  mapper-locations: classpath*:/mybatis/*.xml
  configuration:
    # 主键回填
    use-generated-keys: true
    # 驼峰映射
    map-underscore-to-camel-case: true

redis:
  redisson:
    cluster: shylf_game_game_activity_mng_cluster
    address: "redis://localhost:6379"
    password:
    database: 0
    connectionPoolSize: 32
    timeout: 300
    connection-time-out: 200

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      limit: 100
      keyConvertor: fastjson2 #其他可选：fastjson/jackson
      expireAfterWriteInMillis: 100000
    otherArea:
      type: linkedhashmap
      limit: 100
      keyConvertor: none
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis
      keyConvertor: fastjson2
      broadcastChannel: projectA
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${redis.host}
      port: ${redis.port}
    otherArea:
      type: redis
      keyConvertor: fastjson2 #其他可选：fastjson/jackson
      broadcastChannel: projectA
      valueEncoder: java #其他可选：kryo/kryo5
      valueDecoder: java #其他可选：kryo/kryo5
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${redis.host}
      port: ${redis.port}


api-sign:
  strict-verify:
    enabled: true
springdoc:
  api-docs:
    enabled: true
    path: /v3/docs

# 兑换服务配置
exchange:
  ectask:
    notification:
      # 是否启用EcTask通知（海外机房设置为true，国内机房设置为false或不配置）
      # false或不配置：使用noop实现，依赖DTS监听MySQL变化
      # true：使用Kafka实现，手动发送消息通知
      enabled: false
warp:
  web:
    metadata:
      enabled: false
  databus:
    properties:
      EcTaskLogDelay:
        topic: Game-Exchange-EcTaskDelay-T
        pub:
          app-id: game.activity-base.activity-exchange-service
          group: Game-Exchange-EcTaskDelay-GameActivityBaseActivityExchangeService-P
          token: 55ce4d4cd4b76cccd5426dc74a7d542e
        sub:
          app-id: game.activity-base.activity-exchange-service
          group: Game-Exchange-EcTaskDelay-GameActivityBaseActivityExchangeService-S
          token: 55ce4d4cd4b76cccd5426dc74a7d542e
      EcTaskLogDts:
        topic: Game-Exchange-EcTask-Log-T
        sub:
          appId: game.activity-base.activity-exchange-service
          group: Game-Exchange-EcTask-Log-GameActivityBaseActivityExchangeService-S
          token: 55ce4d4cd4b76cccd5426dc74a7d542e

http:
  server:
    advice:
      enabled: false
    fastjson:
      enabled: false
    port: 8082
  client:
    game-dip-api:
      host: discovery://game.dip.game-dip-api
      connectTimeout: 3000
      readTimeout: 3000
      callTimeout: 4000
      key: 10000000
      secret: yBZMHblUwNsecDUpk9AQikF75CB37X/Ys5h0PBootYk
    game-user-equity-service:
        host: discovery://game.activity-base.game-user-equity-service
        connectTimeout: 400
        readTimeout: 1000
        writeTimeout: 1000
        callTimeout: 1500
        key: 10000000
        secret: yBZMHblUwNsecDUpk9AQikF75CB37X/Ys5h0PBootYk
        maxIdle: 500
        keepAlive: 5000
        zone: sh001
#        scheduler: true
        optional: true
        breaker:
          volumn: 100
          sleep: 1000
          errorRate: 50
          halfOpen: 10
    game-product-api:
      host: http://uat-game-product-api.biligame.com
      connectTimeout: 400
      readTimeout: 1000
      writeTimeout: 1000
      callTimeout: 1500
      key: 10000000
      secret: yBZMHblUwNsecDUpk9AQikF75CB37X/Ys5h0PBootYk