package com.biligame.activity.exchange.dal.repository;

import com.alibaba.fastjson.JSON;
import com.biligame.activity.exchange.aop.cache.Cache;
import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitRecord;
import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitRecordExample;
import com.biligame.activity.exchange.dal.entity.TimesLimitRecordQuery;
import com.biligame.activity.exchange.dal.mapper.ExchItemTimesLimitRecordMapper;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.model.bo.ExchItemTimesLimitRecordBO;
import com.biligame.activity.exchange.model.constants.RedisKey;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/7
 */
@Slf4j
@Repository
public class ExchItemTimesLimitRecordRepository {

    private static final String ACTIVITY_RECORD_CACHE_KEY = "activity:exchange:limit:record:%s:%s:%s:%s";

    @Resource
    private ExchItemTimesLimitRecordMapper exchItemTimesLimitRecordMapper;

    @Resource
    private RedissonClient redissonClient;

    public int insertSelective(ExchItemTimesLimitRecord record) {
        return exchItemTimesLimitRecordMapper.insertSelective(record);
    }

    public int increaseTimes(ExchItemTimesLimitRecordBO old, int count, int limitUpTimes) {
        ExchItemTimesLimitRecord oldRecord = new ExchItemTimesLimitRecord();
        BeanUtils.copyProperties(old, oldRecord);
        return exchItemTimesLimitRecordMapper.increaseTimes(oldRecord, count, limitUpTimes);
    }

    public int decreaseTimes(ExchItemTimesLimitRecordBO old, int count) {
        ExchItemTimesLimitRecord oldRecord = new ExchItemTimesLimitRecord();
        BeanUtils.copyProperties(old, oldRecord);
        return exchItemTimesLimitRecordMapper.decreaseTimes(oldRecord, count);
    }


    public Map<Integer, ExchItemTimesLimitRecordBO> findByUidAndActivityIdAndBizDate(Long uid, String activityId, List<TimesLimitRecordQuery> query) {
        Map<Integer, ExchItemTimesLimitRecordBO> resultMap = new HashMap<>(query.size());

        Map<String, TimesLimitRecordQuery> keyMap = new HashMap<>(query.size());
        List<String> keyList = new ArrayList<>(query.size());
        query.forEach(q -> {
            String key = String.format(ACTIVITY_RECORD_CACHE_KEY, uid, activityId, q.getItemId(), q.getBizDate());
            keyList.add(key);
            keyMap.put(key, q);
        });

        Map<String, String> cacheValueList = redissonClient.getBuckets().get(keyList.toArray(new String[0]));

        cacheValueList.forEach((k, v) -> {
            ExchItemTimesLimitRecordBO bo = JSON.parseObject(v, ExchItemTimesLimitRecordBO.class);
            resultMap.put(bo.getItemId(), bo);
        });

        if (keyList.size() == cacheValueList.size()) {
            return resultMap;
        }

        List<TimesLimitRecordQuery> missGoods = Lists.newArrayList();

        keyList.forEach(key -> {
            if (!cacheValueList.containsKey(key)) {
                missGoods.add(keyMap.get(key));
            }
        });
        List<Integer> missGoodsIds = missGoods.stream().map(TimesLimitRecordQuery::getItemId).collect(Collectors.toList());
        List<String> missBizDate = missGoods.stream().map(TimesLimitRecordQuery::getBizDate).distinct().collect(Collectors.toList());

        ExchItemTimesLimitRecordExample example = new ExchItemTimesLimitRecordExample();
        example.createCriteria().andUidEqualTo(uid).andActivityIdEqualTo(activityId).andBizDateIn(missBizDate).andItemIdIn(missGoodsIds);
        List<ExchItemTimesLimitRecord> recordList = exchItemTimesLimitRecordMapper.selectByExample(example);
        List<ExchItemTimesLimitRecordBO> boList = recordList.stream().map(this::convertBO).collect(Collectors.toList());

        Map<TimesLimitRecordQuery, ExchItemTimesLimitRecordBO> recordMap =
                boList.stream().collect(Collectors.toMap(bo -> new TimesLimitRecordQuery(bo.getBizDate(), bo.getItemId()), Function.identity()));


        ExchItemTimesLimitRecordBO empty = ExchItemTimesLimitRecordBO.emptyObject();
        missGoods.forEach(q -> {
            ExchItemTimesLimitRecordBO bo = recordMap.getOrDefault(q, empty);
            String key = String.format(ACTIVITY_RECORD_CACHE_KEY, uid, activityId, q.getItemId(), q.getBizDate());
            redissonClient.getBucket(key).set(JSON.toJSONString(bo), RedisKey.COMMON_HIT_EXPIRE, TimeUnit.SECONDS);
            resultMap.putIfAbsent(q.getItemId(), bo);
        });

        return resultMap;
    }

    @Cache(prefix = ACTIVITY_RECORD_CACHE_KEY,
            expire = RedisKey.COMMON_HIT_EXPIRE,
            missExpire = RedisKey.COMMON_HIT_EXPIRE,
            clazz = ExchItemTimesLimitRecordBO.class)
    public ExchItemTimesLimitRecordBO findItemRecord(Long uid, String activityId, Integer goodsId, String bizDate) {
        ExchItemTimesLimitRecordExample example = new ExchItemTimesLimitRecordExample();
        example.createCriteria().andUidEqualTo(uid).andActivityIdEqualTo(activityId).andBizDateEqualTo(bizDate).andItemIdEqualTo(goodsId);
        List<ExchItemTimesLimitRecord> recordList = exchItemTimesLimitRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }
        return convertBO(recordList.get(0));
    }

    public ExchItemTimesLimitRecordBO findItemRecordFromDb(Long uid, String activityId, Integer goodsId, String bizDate) {
        return findItemRecord(uid, activityId, goodsId, bizDate);
    }

    public void removeCache(Long uid, String activityId, String bizDate, Integer goodsId) {
        String key = String.format(ACTIVITY_RECORD_CACHE_KEY, uid, activityId, goodsId, bizDate);
        redissonClient.getBucket(key).delete();
    }

    private ExchItemTimesLimitRecordBO convertBO(ExchItemTimesLimitRecord record) {
        ExchItemTimesLimitRecordBO bo = new ExchItemTimesLimitRecordBO();
        BeanUtils.copyProperties(record, bo);
        return bo;
    }

    /**
     * 基于item_uniq_value查询限购记录
     * TODO: 待数据库表结构更新并重新生成MyBatis代码后，使用example查询
     */
    @Cache(prefix = "activity:exchange:limit:record:v2:%s:%s:%s:%s",
            expire = RedisKey.COMMON_HIT_EXPIRE,
            missExpire = RedisKey.COMMON_HIT_EXPIRE,
            clazz = ExchItemTimesLimitRecordBO.class)
    public ExchItemTimesLimitRecordBO findByItemUniqValue(Long uid, String activityId, String itemUniqValue, String bizDate) {
        // 临时实现：先返回null，等待表结构更新
        // TODO: 使用以下代码替换
        /*
        ExchItemTimesLimitRecordExample example = new ExchItemTimesLimitRecordExample();
        example.createCriteria()
                .andUidEqualTo(uid)
                .andActivityIdEqualTo(activityId)
                .andBizDateEqualTo(bizDate)
                .andItemUniqValueEqualTo(itemUniqValue);
        List<ExchItemTimesLimitRecord> recordList = exchItemTimesLimitRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }
        return convertBO(recordList.get(0));
        */
        log.warn("findByItemUniqValue not implemented yet, waiting for table structure update");
        return null;
    }
    
    /**
     * 统一查询方法：优先使用item_uniq_value，如果为空则使用item_id
     */
    public ExchItemTimesLimitRecordBO findRecord(Long uid, String activityId, ExchActivityItemBO item, String bizDate) {
        // 优先使用item_uniq_value查询
        if (StringUtils.isNotBlank(item.getItemUniqValue())) {
            ExchItemTimesLimitRecordBO record = findByItemUniqValue(uid, activityId, item.getItemUniqValue(), bizDate);
            if (record != null) {
                return record;
            }
        }
        // 降级到item_id查询
        return findItemRecord(uid, activityId, item.getId(), bizDate);
    }
    
    public void removeCacheV2(Long uid, String activityId, String bizDate, String itemUniqValue) {
        String key = String.format("activity:exchange:limit:record:v2:%s:%s:%s:%s", uid, activityId, itemUniqValue, bizDate);
        redissonClient.getBucket(key).delete();
    }

    /**
     * 基于统一商品标识查询限购记录
     */
    @Cache(prefix = "activity:exchange:limit:record:%s:%s:%s:%s",
            expire = RedisKey.COMMON_HIT_EXPIRE,
            missExpire = RedisKey.COMMON_HIT_EXPIRE,
            clazz = ExchItemTimesLimitRecordBO.class)
    public ExchItemTimesLimitRecordBO findByUnifiedItemId(Long uid, String activityId, String unifiedItemId, String bizDate) {
        // TODO: 待表结构更新后，使用item_uniq_value查询
        // 目前暂时兼容旧逻辑
        if (unifiedItemId.startsWith("local_")) {
            // 本地商品，使用item_id查询
            String itemIdStr = unifiedItemId.substring(6);
            try {
                Integer itemId = Integer.parseInt(itemIdStr);
                return findItemRecord(uid, activityId, itemId, bizDate);
            } catch (NumberFormatException e) {
                log.error("Invalid local item id: {}", unifiedItemId);
                return null;
            }
        } else {
            // 远程商品，目前返回null，等待表结构更新
            log.warn("Remote item query not implemented yet, waiting for table update: {}", unifiedItemId);
            return null;
        }
    }
    
    public void removeCacheByUnifiedId(Long uid, String activityId, String bizDate, String unifiedItemId) {
        String key = String.format(ACTIVITY_RECORD_CACHE_KEY, uid, activityId, unifiedItemId, bizDate);
        redissonClient.getBucket(key).delete();
    }

}
