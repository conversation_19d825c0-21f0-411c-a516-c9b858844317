package com.biligame.activity.exchange.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/21 下午7:09
 */
@Data
@EqualsAndHashCode
public class ExchangeDoRequest {
    /**
     * 活动id
     */
    @NotNull
    @NotBlank(message = "活动id不能为空")
    @Schema(description = "活动id")
    private String activityId;
    /**
     * 用户id
     */
    @NotNull
//    @NotBlank(message = "用户id不能为空")
    private Long uid;

    /**
     * 兑换物品ID（兼容老接口，本地商品使用）
     * 新接口建议使用itemUniqueId
     */
//    @NotNull
//    @NotBlank(message = "兑换物品ID不能为空")
    @Schema(description = "兑换物品ID（本地商品使用）", deprecated = true)
    private String itemId;
    
    /**
     * 兑换物品唯一标识
     * 对于本地商品：等同于itemId
     * 对于积分商城商品：为product_id
     */
    @Schema(description = "兑换物品唯一标识（推荐使用）")
    private String itemUniqueId;

    /**
     * 兑换数量
     */
    @NotNull
    @Min(value = 1)
    @Schema(description = "兑换数量")
    private Integer count;
    /**
     * 幂等ID
     */
    @NotNull
    @NotBlank
    @Schema(description = "幂等ID")
    private String bizId;

    /**
     * 额外信息 根据业务扩展
     */
    @Schema(description = "额外信息")
    private Map<String, Object> extraData;
    
    /**
     * 获取有效的商品标识
     * 优先使用itemUniqueId，如果为空则使用itemId
     */
    public String getEffectiveItemId() {
        return itemUniqueId != null && !itemUniqueId.trim().isEmpty() ? 
                itemUniqueId : itemId;
    }
}
