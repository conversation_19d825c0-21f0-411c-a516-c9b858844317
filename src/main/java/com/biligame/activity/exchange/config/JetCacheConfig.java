package com.biligame.activity.exchange.config;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.JetCacheBaseBeans;
import com.biligame.activity.exchange.config.redis.RedissonProperties;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @create 2025/6/27 19:40
 * @description
 */
@Configuration
@EnableMethodCache(basePackages = {"com.biligame.activity.dal", "com.biligame.activity.remote"})
@Import(JetCacheBaseBeans.class)
public class JetCacheConfig {
    
    @Bean
    public RedisClient redisClient(RedissonProperties redissonProperties) {
        // Parse Redis URI from address
        String address = redissonProperties.getAddress();
        RedisURI.Builder uriBuilder = RedisURI.builder();

        // Handle redis:// prefix
        if (address.startsWith("redis://")) {
            String hostPort = address.substring(8); // Remove "redis://" prefix
            String[] parts = hostPort.split(":");
            uriBuilder.withHost(parts[0]);
            if (parts.length > 1) {
                uriBuilder.withPort(Integer.parseInt(parts[1]));
            }
        } else {
            // Assume it's host:port format
            String[] parts = address.split(":");
            uriBuilder.withHost(parts[0]);
            if (parts.length > 1) {
                uriBuilder.withPort(Integer.parseInt(parts[1]));
            }
        }

        // Set database
        uriBuilder.withDatabase(redissonProperties.getDatabase());

        // Set password if provided
        if (redissonProperties.getPassword() != null && !redissonProperties.getPassword().trim().isEmpty()) {
            uriBuilder.withPassword(redissonProperties.getPassword().toCharArray());
        }

        RedisURI redisURI = uriBuilder.build();

        // Create RedisClient with options
        RedisClient client = RedisClient.create(redisURI);

        // Configure client options
        ClientOptions clientOptions = ClientOptions.builder()
                .autoReconnect(true)
                .build();

        client.setOptions(clientOptions);

        return client;
    }
}
