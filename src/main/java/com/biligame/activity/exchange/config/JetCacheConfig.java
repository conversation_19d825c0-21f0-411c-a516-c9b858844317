package com.biligame.activity.exchange.config;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.JetCacheBaseBeans;
import com.biligame.activity.exchange.config.redis.RedissonProperties;
import io.lettuce.core.RedisClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @create 2025/6/27 19:40
 * @description
 */
@Configuration
@EnableMethodCache(basePackages = {"com.biligame.activity.dal", "com.biligame.activity.remote"})
@Import(JetCacheBaseBeans.class)
public class JetCacheConfig {
    
    @Bean
    public RedisClient redisClient(RedissonProperties redissonProperties) {
        return RedisClient.create(redissonProperties.getAddress());
    }
}
