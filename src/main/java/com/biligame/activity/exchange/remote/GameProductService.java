package com.biligame.activity.exchange.remote;

import com.biligame.activity.exchange.aop.cache.Cache;
import com.biligame.activity.exchange.dto.equity.UserEquityFreezeDTO;
import com.biligame.activity.exchange.dto.product.WepPayProductDTO;
import com.biligame.activity.exchange.dto.product.WepPayProductQueryRequest;
import com.biligame.activity.exchange.enums.ApiCodeEnum;
import com.biligame.activity.exchange.exception.BusinessException;
import com.biligame.activity.exchange.remote.rpc.GameProductAPI;
import com.biligame.activity.exchange.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;
import pleiades.component.http.client.response.BiliDataApiResponse;
import retrofit2.Response;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2024/8/20 18:57
 * @description
 */
@Slf4j
@Service
public class GameProductService {
    
    @Resource
    private GameProductAPI gameProductAPI;
    
    @Cache(prefix = "game:product:query", clazz = WepPayProductDTO.class, expire = 300)
    public BiliDataApiResponse<WepPayProductDTO> queryProduct(WepPayProductQueryRequest request) {
        
        Response<BiliDataApiResponse<WepPayProductDTO>> response = null;
        RequestBody body = RequestBody.create(JacksonUtils.toJsonStr(request), MediaType.parse("application/json"));
        try {
            response = gameProductAPI.queryProduct(body).execute();
        } catch (Exception e) {
            throw new BusinessException(ApiCodeEnum.NETWORK_COMMUNICATION_ERROR_EXT, e.getMessage());
        }
        
        BiliDataApiResponse<WepPayProductDTO> result;
        if (response == null || !response.isSuccessful() || (result = response.body()) == null) {
            throw new BusinessException(ApiCodeEnum.NETWORK_COMMUNICATION_ERROR_EXT, response == null ? "response is null" : response.message());
        }
        
        log.info("GameProductService queryProduct, params:{}, result:{}", request, result);
        return result;
    }
}
