package com.biligame.activity.exchange.factory;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 兑换服务工厂管理器
 * 管理所有的工厂实现
 */
@Slf4j
@Component
public class ExchangeServiceFactoryManager {
    
    private final Map<ActivityTypeEnum, ExchangeServiceFactory> factoryMap = new ConcurrentHashMap<>();
    
    @Autowired
    private List<ExchangeServiceFactory> factories;
    
    @PostConstruct
    public void init() {
        for (ExchangeServiceFactory factory : factories) {
            factoryMap.put(factory.getSupportType(), factory);
            log.info("Register ExchangeServiceFactory: {} for type: {}", 
                    factory.getClass().getSimpleName(), factory.getSupportType());
        }
    }
    
    /**
     * 根据活动类型获取对应的工厂
     */
    public ExchangeServiceFactory getFactory(ActivityTypeEnum activityType) {
        ExchangeServiceFactory factory = factoryMap.get(activityType);
        if (factory == null) {
            throw new IllegalArgumentException("No factory found for activity type: " + activityType);
        }
        return factory;
    }
} 