package com.biligame.activity.exchange.factory.impl;

import com.biligame.activity.exchange.dal.repository.ExchActivityItemRepository;
import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.factory.ExchangeServiceFactory;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.service.*;
import com.biligame.activity.exchange.service.impl.limit.DefaultTimesLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 普通兑换活动服务工厂
 */
@Slf4j
@Component
public class CommonExchangeServiceFactory implements ExchangeServiceFactory {
    
    @Resource
    private AssetsService chipAssetsService;
    
    @Resource
    private ExchangeServiceContainer exchangeServiceContainer;
    
    @Resource
    private DefaultTimesLimitService defaultTimesLimitService;
    
    @Resource
    private ExchActivityItemRepository exchActivityItemRepository;
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.COMMON;
    }
    
    @Override
    public AssetsService createAssetsService() {
        return chipAssetsService;
    }
    
    @Override
    public StockService createStockService() {
        // 根据商品的库存类型动态返回
        // 这里暂时返回null，需要在使用时根据商品信息决定
        return null;
    }
    
    @Override
    public ExchangeItemService createExchangeItemService() {
        // 普通活动的商品服务
        return new CommonExchangeItemService(exchActivityItemRepository);
    }
    
    @Override
    public TimesLimitService createTimesLimitService() {
        return defaultTimesLimitService;
    }
    
    @Override
    public ItemReleaseService createItemReleaseService() {
        // TODO: 根据商品类型返回对应的发放服务
        return null;
    }
    
    @Override
    public ExchLimitService createExchLimitService() {
        // 根据商品的兑换门槛类型动态返回
        return null;
    }
    
    /**
     * 普通活动的商品服务
     */
    @Slf4j
    private static class CommonExchangeItemService implements ExchangeItemService {
        
        private final ExchActivityItemRepository exchActivityItemRepository;
        
        public CommonExchangeItemService(ExchActivityItemRepository exchActivityItemRepository) {
            this.exchActivityItemRepository = exchActivityItemRepository;
        }
        
        @Override
        public List<ExchActivityItemBO> findByActivityId(String activityId) {
            return exchActivityItemRepository.findByActivityId(activityId);
        }
        
        @Override
        public ExchActivityItemBO findByActivityIdAndUniqueId(String activityId, String uniqueId) {
            // 对于普通活动，uniqueId就是本地的itemId
            try {
                Integer itemId = Integer.valueOf(uniqueId);
                return exchActivityItemRepository.get(itemId);
            } catch (NumberFormatException e) {
                log.error("Invalid itemId for common activity: {}", uniqueId);
                return null;
            }
        }
        
        @Override
        public ActivityTypeEnum getSupportType() {
            return ActivityTypeEnum.COMMON;
        }
    }
} 