package com.biligame.activity.exchange.factory.impl;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.factory.ExchangeServiceFactory;
import com.biligame.activity.exchange.service.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 通用活动兑换服务工厂
 */
@Component
public class CommonExchangeServiceFactory extends ExchangeServiceFactory {
    
    @Resource
    private ExchangeServiceContainer serviceContainer;
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.COMMON;
    }
    
    @Override
    public AssetsService createAssetsService() {
        // 通用活动使用普通资产服务
        return serviceContainer.getAssetsService(ActivityTypeEnum.COMMON);
    }
    
    @Override
    public ExchangeItemService createExchangeItemService() {
        // 通用活动使用普通商品服务
        return serviceContainer.getExchangeItemService(ActivityTypeEnum.COMMON);
    }
    
    // 其他方法使用父类的默认实现
    // - createStockService: 使用父类根据item.stockType从容器获取
    // - createTimesLimitService: 使用默认限购服务
    // - createItemReleaseService: 使用父类根据item.itemType从容器获取
    // - createExchLimitService: 使用父类根据item.exchLimitType从容器获取
} 