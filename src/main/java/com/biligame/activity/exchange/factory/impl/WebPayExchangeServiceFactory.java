package com.biligame.activity.exchange.factory.impl;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.factory.ExchangeServiceFactory;
import com.biligame.activity.exchange.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 积分商城兑换服务工厂
 */
@Slf4j
@Component
public class WebPayExchangeServiceFactory implements ExchangeServiceFactory {
    
    @Resource
    private AssetsService pointAssetsService;
    
    @Resource
    private StockService noLimitStockServiceImpl;
    
    @Resource
    private ExchangeItemService webPayExchangeItemService;
    
    @Resource
    private TimesLimitService webPayTimesLimitService;
    
    @Resource
    private ExchLimitService noneExchLimitServiceImpl;
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.WEB_PAY;
    }
    
    @Override
    public AssetsService createAssetsService() {
        return pointAssetsService;
    }
    
    @Override
    public StockService createStockService() {
        // 积分商城默认使用无限库存
        return noLimitStockServiceImpl;
    }
    
    @Override
    public ExchangeItemService createExchangeItemService() {
        return webPayExchangeItemService;
    }
    
    @Override
    public TimesLimitService createTimesLimitService() {
        return webPayTimesLimitService;
    }
    
    @Override
    public ItemReleaseService createItemReleaseService() {
        // TODO: 创建WebPayItemReleaseService
        return null;
    }
    
    @Override
    public ExchLimitService createExchLimitService() {
        // 积分商城默认无兑换门槛
        return noneExchLimitServiceImpl;
    }
} 