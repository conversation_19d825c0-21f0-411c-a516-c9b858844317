package com.biligame.activity.exchange.factory.impl;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.enums.StockTypeEnum;
import com.biligame.activity.exchange.factory.ExchangeServiceFactory;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.service.*;
import com.biligame.activity.exchange.service.impl.stock.NoLimitStockServiceImpl;
import com.biligame.activity.exchange.service.impl.limit.WebPayTimesLimitService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 积分商城兑换服务工厂
 */
@Component
public class WebPayExchangeServiceFactory extends ExchangeServiceFactory {
    
    @Resource
    private AssetsService pointAssetsService;
    
    @Resource
    private ExchangeItemService webPayExchangeItemService;
    
    @Resource
    private WebPayTimesLimitService webPayTimesLimitService;
    
    @Resource
    private NoLimitStockServiceImpl noLimitStockService;
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.WEB_PAY;
    }
    
    @Override
    public AssetsService createAssetsService() {
        return pointAssetsService;
    }
    
    @Override
    public ExchangeItemService createExchangeItemService() {
        return webPayExchangeItemService;
    }
    
    @Override
    public StockService createStockService(ExchActivityItemBO item) {
        // 积分商城总是使用无限库存
        return noLimitStockService;
    }
    
    @Override
    public TimesLimitService createTimesLimitService(ExchActivityItemBO item) {
        // 积分商城使用专门的限购服务
        return webPayTimesLimitService;
    }
    
    // 其他方法使用父类的默认实现
    // - createItemReleaseService: 使用父类根据item.itemType从容器获取
    // - createExchLimitService: 使用父类根据item.exchLimitType从容器获取
} 