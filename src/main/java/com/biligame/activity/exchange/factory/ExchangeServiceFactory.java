package com.biligame.activity.exchange.factory;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.service.*;

/**
 * 兑换服务抽象工厂
 * 根据活动类型创建对应的服务组
 */
public interface ExchangeServiceFactory {
    
    /**
     * 获取支持的活动类型
     */
    ActivityTypeEnum getSupportType();
    
    /**
     * 创建资产服务
     */
    AssetsService createAssetsService();
    
    /**
     * 创建库存服务（可能根据商品的库存类型返回不同实现）
     */
    StockService createStockService();
    
    /**
     * 创建商品服务
     */
    ExchangeItemService createExchangeItemService();
    
    /**
     * 创建限购服务
     */
    TimesLimitService createTimesLimitService();
    
    /**
     * 创建商品发放服务
     */
    ItemReleaseService createItemReleaseService();
    
    /**
     * 创建兑换门槛服务
     */
    ExchLimitService createExchLimitService();
} 