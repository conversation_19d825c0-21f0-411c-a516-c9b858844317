package com.biligame.activity.exchange.factory;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.enums.ItemTypeEnum;
import com.biligame.activity.exchange.enums.ExchLimitTypeEnum;
import com.biligame.activity.exchange.enums.StockTypeEnum;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.service.*;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 兑换服务抽象工厂
 * 根据活动类型创建对应的服务组
 * 提供默认实现，子类可以选择覆盖
 */
@Slf4j
public abstract class ExchangeServiceFactory {
    
    @Resource
    protected ExchangeServiceContainer exchangeServiceContainer;
    
    /**
     * 获取支持的活动类型
     */
    public abstract ActivityTypeEnum getSupportType();
    
    /**
     * 创建资产服务
     * 子类必须实现
     */
    public abstract AssetsService createAssetsService();
    
    /**
     * 创建库存服务
     * 默认根据商品的库存类型从容器获取
     */
    public StockService createStockService(ExchActivityItemBO item) {
        if (item == null) {
            return null;
        }
        StockService service = exchangeServiceContainer.getStockService(item.getStockType());
        if (service == null) {
            log.warn("No StockService found for type: {}", item.getStockType());
        }
        return service;
    }
    
    /**
     * 创建商品服务
     * 子类必须实现
     */
    public abstract ExchangeItemService createExchangeItemService();
    
    /**
     * 创建限购服务
     * 默认返回容器中的默认限购服务
     */
    public TimesLimitService createTimesLimitService(ExchActivityItemBO item) {
        // 子类可以根据需要覆盖此方法
        return exchangeServiceContainer.getDefaultTimesLimitService();
    }
    
    /**
     * 创建商品发放服务
     * 默认根据商品类型从容器获取
     */
    public ItemReleaseService createItemReleaseService(ExchActivityItemBO item) {
        if (item == null) {
            return null;
        }
        ItemReleaseService service = exchangeServiceContainer.getItemReleaseService(item.getItemType());
        if (service == null) {
            log.warn("No ItemReleaseService found for type: {}", item.getItemType());
        }
        return service;
    }
    
    /**
     * 创建兑换门槛服务
     * 默认根据商品的门槛类型从容器获取
     */
    public ExchLimitService createExchLimitService(ExchActivityItemBO item) {
        if (item == null) {
            return null;
        }
        ExchLimitService service = exchangeServiceContainer.getExchLimitService(item.getExchLimitTypeEnum());
        if (service == null) {
            log.warn("No ExchLimitService found for type: {}", item.getExchLimitTypeEnum());
        }
        return service;
    }
} 