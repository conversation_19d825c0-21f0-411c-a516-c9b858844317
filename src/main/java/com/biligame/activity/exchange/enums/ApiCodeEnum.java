package com.biligame.activity.exchange.enums;

import com.biligame.activity.exchange.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API response code enumeration
 */
@Getter
@AllArgsConstructor
public enum ApiCodeEnum {

    SUCCESS(0, "Success"),
    SYSTEM_BUSY(-500, "Server internal error"),

    // BDIP related exceptions (-5XX)
    BDIP_REQUEST_ERROR(-507, "Traffic congestion, please try again later"),
    BDIP_SEND_MAIL_FAIL(-508, "Mail sending failed"),
    NETWORK_COMMUNICATION_ERROR_EXT(-509, "Network communication error: {0}"),

    CODE_USER_NOT_LOGIN(-101, "User not logged in"),
    CODE_REQ_ERROR(-400, "Request error"),
    CODE_REQ_LIMIT(-405, "Request too frequent"),
    MISSING_REQUIRED_PARAM(-406, "Required parameter missing"),
    // Remote call response error code
    REMOTE_REQUEST_ERROR(2301, "Interface request exception"),

    ILLEGAL_ACCESS_REQUEST(11002, "Illegal request"),


    CODE_ACTIVITY_NOT_FUND(20001, "Activity not found"),
    CODE_ACTIVITY_NOT_START(20002, "Activity not started"),
    CODE_ACTIVITY_HAS_ENDED(20003, "Activity has ended"),
    CODE_ITEM_NOT_EXIST(20004, "Exchange item does not exist"),
    EXCHANGE_STOCK_NOT_ENOUGH(20005, "Insufficient stock"),
    EXCHANGE_ASSET_FREEZE_FAIL(20006, "Asset freeze failed"),
    EXCHANGE_STOCK_DEDUCT_FAIL(20007, "Stock deduction failed"),
    EXCHANGE_FAILED(20008, "Exchange failed"),
    EXCHANGE_NOT_OPEN(20009, "Exchange time has not started yet"),
    EXCHANGE_OUT_OF_LIMIT(20010, "Exchange limit reached for this item"),
    EXCHANGE_ASSET_NOT_ENOUGH(20011, "Insufficient assets"),
    EXCHANGE_TIMES_LIMIT_FAIL(20012, "Exchange limit reached for this item"),
    EXCHANGE_EXCH_LIMIT(20013, "Exchange threshold not met"),

    EXCHANGE_ING(20014, "Exchange in progress"),
    EXCHANGE_CANCEL(20015, "Exchange order cancelled"),
    ;

    private final Integer value;
    private final String message;

    public Integer value() {
        return this.value;
    }

    public String message() {
        return this.message;
    }

    public BusinessException createException(String message) {
        return new BusinessException(this.value, message);
    }

    public BusinessException createException() {
        return new BusinessException(this.value, this.message);
    }
}
