package com.biligame.activity.exchange.enums;

import com.biligame.activity.exchange.exception.BusinessException;
import com.biligame.activity.exchange.utils.MessageUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API response code enumeration
 */
@Getter
@AllArgsConstructor
public enum ApiCodeEnum {

    SUCCESS(0, "api.code.success"),
    SYSTEM_BUSY(-500, "api.code.system.busy"),

    // BDIP related exceptions (-5XX)
    BDIP_REQUEST_ERROR(-507, "api.code.bdip.request.error"),
    BDIP_SEND_MAIL_FAIL(-508, "api.code.bdip.send.mail.fail"),
    NETWORK_COMMUNICATION_ERROR_EXT(-509, "api.code.network.communication.error"),

    CODE_USER_NOT_LOGIN(-101, "api.code.user.not.login"),
    CODE_REQ_ERROR(-400, "api.code.request.error"),
    CODE_REQ_LIMIT(-405, "api.code.request.limit"),
    MISSING_REQUIRED_PARAM(-406, "api.code.missing.required.param"),
    // Remote call response error code
    REMOTE_REQUEST_ERROR(2301, "api.code.remote.request.error"),

    ILLEGAL_ACCESS_REQUEST(11002, "api.code.illegal.access.request"),


    CODE_ACTIVITY_NOT_FUND(20001, "api.code.activity.not.found"),
    CODE_ACTIVITY_NOT_START(20002, "api.code.activity.not.start"),
    CODE_ACTIVITY_HAS_ENDED(20003, "api.code.activity.has.ended"),
    CODE_ITEM_NOT_EXIST(20004, "api.code.item.not.exist"),
    EXCHANGE_STOCK_NOT_ENOUGH(20005, "api.code.exchange.stock.not.enough"),
    EXCHANGE_ASSET_FREEZE_FAIL(20006, "api.code.exchange.asset.freeze.fail"),
    EXCHANGE_STOCK_DEDUCT_FAIL(20007, "api.code.exchange.stock.deduct.fail"),
    EXCHANGE_FAILED(20008, "api.code.exchange.failed"),
    EXCHANGE_NOT_OPEN(20009, "api.code.exchange.not.open"),
    EXCHANGE_OUT_OF_LIMIT(20010, "api.code.exchange.out.of.limit"),
    EXCHANGE_ASSET_NOT_ENOUGH(20011, "api.code.exchange.asset.not.enough"),
    EXCHANGE_TIMES_LIMIT_FAIL(20012, "api.code.exchange.times.limit.fail"),
    EXCHANGE_EXCH_LIMIT(20013, "api.code.exchange.threshold.not.met"),

    EXCHANGE_ING(20014, "api.code.exchange.in.progress"),
    EXCHANGE_CANCEL(20015, "api.code.exchange.cancelled"),
    ;

    private final Integer value;
    private final String message;

    public Integer value() {
        return this.value;
    }

    public String message() {
        return this.message;
    }

    public BusinessException createException(String message) {
        return new BusinessException(this.value, message);
    }

    public BusinessException createException() {
        return new BusinessException(this.value, this.message);
    }
}
