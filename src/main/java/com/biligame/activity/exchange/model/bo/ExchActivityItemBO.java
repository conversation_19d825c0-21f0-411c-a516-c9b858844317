package com.biligame.activity.exchange.model.bo;

import com.biligame.activity.exchange.enums.ExchLimitTypeEnum;
import com.biligame.activity.exchange.enums.ItemTypeEnum;
import com.biligame.activity.exchange.enums.StockTypeEnum;
import com.biligame.activity.exchange.enums.TimesLimitType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/24 下午10:48
 */
@Data
public class ExchActivityItemBO {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 活动id
     */
    private String activityId;
    /**
     * 活动开始时间
     */
    private Integer showStartTime;

    /**
     * 活动结束时间
     */
    private Integer showEndTime;

    /**
     * 兑换开始时间
     */
    private Integer exchangeStartTime;

    /**
     * 兑换结束时间
     */
    private Integer exchangeEndTime;

    /**
     * 兑换物名称
     */
    private String itemName;

    /**
     * 兑换物图片
     */
    private String itemImg;

    /**
     * 兑换物描述
     */
    private String itemDesc;

    /**
     * 兑换物类型
     */
    private ItemTypeEnum itemType;

    /**
     * 兑换物唯一标记,SPUD_ID/SKU_ID/券码
     */
    private String itemUniqValue;

    /**
     * 兑换价值
     */
    private Integer price;

    /**
     * 库存类型0:无限库存 1. 兑换中心库存体系
     */
    private StockTypeEnum stockType;

    /**
     * 库存标识ID
     */
    private String stockId;

    /**
     * 展示优先级
     */
    private Integer priority;

    /**
     * 兑换次数限制类型.none:无限制 day: 每天限制 all: 活动期间限制 week: 每周限制
     * @see TimesLimitType
     */
    private String timesLimitType;

    /**
     * 兑换限制次数
     */
    private Integer timesLimitValue;

    /**
     * 限制门槛类型 node：无限制 ip: IP等级限制 new: 新客限制
     */
    private String exchLimitType;

    /**
     * 限制门槛类型 枚举
     */
    private ExchLimitTypeEnum exchLimitTypeEnum;

    /**
     * 限制门槛值 node: 空字符串 ip： ipId+level
     */
    private String exchLimitValue;

    /**
     * 限制门槛 相关跳转url地址
     */
    private String exchLimitJumpUrl;

    /**
     * 商品标签
     */
    private String tags;

    /**
     * 扩展信息
     */
    private String ext;

    /**
     * 扩展信息对象
     */
    private ExchItemExtBO extBO;
    /**
     * 是否已经启用 1：启用 0： 未启用
     */
    private Boolean isEnable;

    /**
     * 第一次启用时间
     */
    private Integer enableTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 获取统一的商品标识
     * 对于本地商品(item_uniq_value为空)，返回 "local_" + id
     * 对于远程商品，返回 item_uniq_value
     */
    public String getUnifiedItemId() {
        if (StringUtils.isNotBlank(itemUniqValue)) {
            return itemUniqValue;
        }
        // 对于本地商品，使用 "local_" 前缀 + item_id
        return "local_" + id;
    }
}
