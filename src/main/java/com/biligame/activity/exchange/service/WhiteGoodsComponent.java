package com.biligame.activity.exchange.service;

import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.biligame.activity.exchange.enums.ItemTypeEnum;
import com.biligame.activity.exchange.model.constants.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/12/4 15:54
 * @description 白名单商品
 */
@Slf4j
@Component
public class WhiteGoodsComponent {

    @Value("${white-goods-id:0000}")
    @DynamicValue
    private Set<String> whiteGoodsIdList;

    @Value("${white-uid:0000}")
    @DynamicValue
    private Set<Long> whiteUidList;

    @Value("${magic-value-shop-black-list:0000}")
    @DynamicValue
    private Set<Long> magicValueShopBlackList;

    @Value("${brave-cat-shop-black-list:0000}")
    @DynamicValue
    private Set<Long> braveCatShopBlackList;

    @Resource
    private StringRedisTemplate redisTemplate;

    /**
     * 检查用户是否在白名单中可以看到该商品
     * 
     * @param uid 用户ID
     * @param itemUniqueId 商品唯一标识(item_uniq_value)
     * @param itemType 商品类型（可选，用于特殊处理）
     * @return 是否允许
     */
    public boolean isAllowed(Long uid, String itemUniqueId, ItemTypeEnum itemType) {
        // 对于空的itemUniqueId，允许通过（向后兼容）
        if (itemUniqueId == null || itemUniqueId.trim().isEmpty()) {
            return true;
        }
        
        // 检查是否在白名单中
        if (!whiteGoodsIdList.contains(itemUniqueId)) {
            return true;
        }
        
        return uid != null && whiteUidList.contains(uid);
    }
    
    /**
     * 兼容旧接口
     */
    public boolean isAllowed(Long uid, String itemId) {
        return isAllowed(uid, itemId, null);
    }

    public boolean hitBlack(int shopType, Long uid) {
        if (shopType == 3) {
            return magicValueShopBlackList.contains(uid);
        }
        if (shopType == 4) {
            return braveCatShopBlackList.contains(uid);
        }
        return false;
    }
}
