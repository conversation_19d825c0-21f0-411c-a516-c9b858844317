package com.biligame.activity.exchange.service;

import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @version 1.0
 */
@Component
public class WhiteGoodsComponent {

    @Value("${white.component.uid.list:}")
    @DynamicValue
    private Set<Long> whiteUidList;

    @Value("${white.component.goods.list:}")
    @DynamicValue
    private Set<String> whiteGoodsIdList;

    @Value("${magic-value.shop.black.list:}")
    @DynamicValue
    private Set<Long> magicValueShopBlackList;

    @Value("${brave-cat.shop.black.list:}")
    @DynamicValue
    private Set<Long> braveCatShopBlackList;

    /**
     * 兑换物白名单逻辑
     *
     * @param uid      用户id
     * @param goodsId  兑换物id
     * @return
     */
    public boolean isAllowed(Long uid, String goodsId) {
        if (!whiteGoodsIdList.contains(goodsId)) {
            return true;
        }
        return uid != null && whiteUidList.contains(uid);
    }

    public boolean hitBlack(int shopType, Long uid) {
        if (shopType == 3) {
            return magicValueShopBlackList.contains(uid);
        }
        if (shopType == 4) {
            return braveCatShopBlackList.contains(uid);
        }
        return false;
    }
}
