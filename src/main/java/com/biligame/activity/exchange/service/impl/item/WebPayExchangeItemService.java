package com.biligame.activity.exchange.service.impl.item;

import com.biligame.activity.exchange.dal.entity.ExchActivityItem;
import com.biligame.activity.exchange.dal.repository.ExchActivityItemRepository;
import com.biligame.activity.exchange.dal.repository.ExchActivityRepository;
import com.biligame.activity.exchange.dto.product.WepPayProductDTO;
import com.biligame.activity.exchange.dto.product.WepPayProductQueryRequest;
import com.biligame.activity.exchange.dto.resp.WebPayItemInfoDTO;
import com.biligame.activity.exchange.dto.resp.WebPayItemQueryDTO;
import com.biligame.activity.exchange.enums.*;
import com.biligame.activity.exchange.exception.BusinessException;
import com.biligame.activity.exchange.model.bo.ExchActivityBO;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.remote.GameProductService;
import com.biligame.activity.exchange.service.impl.AbstractExchangeItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import pleiades.component.http.client.response.BiliDataApiResponse;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/8/20 19:31
 * @description 网页充值（积分商城）的兑换物服务实现
 */
@Slf4j
@Service("webPayExchangeItemService")
public class WebPayExchangeItemService extends AbstractExchangeItemService {
    
    @Resource
    private GameProductService gameProductService;
    
    @Resource
    private ExchActivityRepository exchActivityRepository;
    
    @Resource
    private ExchActivityItemRepository exchActivityItemRepository;
    
    @Override
    public List<ExchActivityItemBO> findByActivityId(String activityId) {
        
        List<ExchActivityItemBO> exchActivityItemBOList = exchActivityItemRepository.findByActivityId(activityId);
        log.info("WebPayExchangeItemService findByActivityId get from DB, activityId:{}, size:{}", activityId, exchActivityItemBOList.size());
        return exchActivityItemBOList;
    }
    
    @Override
    public ExchActivityItemBO findByActivityIdAndUniqueId(String activityId, String uniqueId) {
        // 对于积分商城，uniqueId就是product_id
        if (uniqueId == null || uniqueId.trim().isEmpty()) {
            log.error("findByActivityIdAndUniqueId: uniqueId is empty");
            return null;
        }
        
        try {
            // 构建查询请求
            WepPayProductQueryRequest queryRequest = new WepPayProductQueryRequest();
            queryRequest.setProductId(uniqueId);
            
            // 从远程获取商品信息
            BiliDataApiResponse<WepPayProductDTO> response = gameProductService.queryProduct(queryRequest);
            if (response == null || response.getCode() != 0 || response.getData() == null) {
                log.error("Product not found by product_id: {}", uniqueId);
                return null;
            }
            
            WepPayProductDTO product = response.getData();
            
            // 构建商品BO
            return buildFromRemoteProduct(product, activityId);
        } catch (Exception e) {
            log.error("Failed to get product by uniqueId: {}", uniqueId, e);
            return null;
        }
    }
    
    /**
     * 从远程商品构建ExchActivityItemBO
     */
    private ExchActivityItemBO buildFromRemoteProduct(WepPayProductDTO product, String activityId) {
        ExchActivityItemBO bo = new ExchActivityItemBO();
        
        // 使用负数ID作为虚拟ID，避免与本地商品ID冲突
        bo.setId(-Math.abs(product.getProductId().hashCode()));
        bo.setActivityId(activityId);
        bo.setItemName(product.getProductName());
        bo.setItemDesc(product.getProductName()); // 暂时使用商品名作为描述
        bo.setItemImg(""); // 远程商品暂无图片
        bo.setPrice(product.getPrice() != null ? product.getPrice() : 0);
        bo.setItemType(ItemTypeEnum.WEB_PAY_ITEM); // 使用正确的枚举值
        
        // 积分商城商品的uniqueValue就是product_id
        bo.setItemUniqValue(product.getProductId());
        
        // 设置默认值
        bo.setStockType(StockTypeEnum.NO_LIMIT);
        bo.setStockId("");
        bo.setIsEnable(true); // 默认启用
        bo.setExchLimitType(ExchLimitTypeEnum.NONE.getType());
        
        // 根据商品的限购周期设置限购类型
        if (product.getLimitPeriod() != null && product.getLimitPeriod().getTimesLimitType() != null) {
            bo.setTimesLimitType(product.getLimitPeriod().getTimesLimitType().getType());
        } else {
            bo.setTimesLimitType(TimesLimitType.NONE.getType()); // 默认无限制
        }
        
        bo.setTimesLimitValue(product.getLimitQuantity() != null ? 
                product.getLimitQuantity() : 0);
        bo.setPriority(0); // 默认优先级
        
        // 时间设置
        Date now = new Date();
        bo.setCtime(now);
        bo.setMtime(now);
        bo.setShowStartTime(0);
        bo.setShowEndTime(0);
        bo.setExchangeStartTime(0);
        bo.setExchangeEndTime(0);
        
        return bo;
    }
    
    public WebPayItemQueryDTO findByActivityIdAndUniqueIds(String activityId, List<String> uniqueIds) {
        
        ExchActivityBO exchActivityBO = exchActivityRepository.get(activityId);
        if (exchActivityBO == null) {
            log.error("WebPayExchangeItemService findByActivityIdAndUniqueIds exchActivityBO is null, activityId:{}", activityId);
            throw new BusinessException(ApiCodeEnum.CODE_ACTIVITY_NOT_FUND);
        }
        
        // 批量从远程获取商品信息
        List<WebPayItemInfoDTO> webPayItemInfoDTOList = uniqueIds.stream()
                .map(uniqueId -> queryFromRemote(exchActivityBO, uniqueId))
                .filter(Objects::nonNull)
                .map(item -> {
                    WebPayItemInfoDTO webPayItemInfoDTO = new WebPayItemInfoDTO();
                    webPayItemInfoDTO.setItemId(item.getId());
                    webPayItemInfoDTO.setProductId(item.getItemUniqValue());
                    webPayItemInfoDTO.setItemName(item.getItemName());
                    return webPayItemInfoDTO;
                })
                .collect(Collectors.toList());
        
        WebPayItemQueryDTO webPayItemQueryDTO = new WebPayItemQueryDTO();
        webPayItemQueryDTO.setActivityId(activityId);
        webPayItemQueryDTO.setGameId(exchActivityBO.getGameId());
        webPayItemQueryDTO.setItemInfos(webPayItemInfoDTOList);
        
        return webPayItemQueryDTO;
    }
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.WEB_PAY;
    }
    
    private ExchActivityItemBO queryFromRemote(ExchActivityBO exchActivityBO, String uniqueId) {
        WepPayProductQueryRequest request = new WepPayProductQueryRequest();
        request.setBizType(WepPayProductQueryRequest.bizTypeEnum.POINT_MALL.name());
        request.setGameBaseId(exchActivityBO.getGameBaseId());
        request.setGameId(exchActivityBO.getGameId());
        request.setProductId(uniqueId);
        BiliDataApiResponse<WepPayProductDTO> response = gameProductService.queryProduct(request);
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("WebPayExchangeItemService queryFromRemote failed, request:{}, response: {}", request,response);
            return null;
        }
        return buildBO(response.getData(), exchActivityBO);
    }
    
    private ExchActivityItemBO buildBO(WepPayProductDTO productDTO, ExchActivityBO exchActivityBO) {
        ExchActivityItemBO bo = new ExchActivityItemBO();
        // 为远程商品生成虚拟ID，使用负数避免与本地ID冲突
        // 使用productId的hashCode作为虚拟ID，确保同一个商品的ID始终相同
        bo.setId(Math.abs(productDTO.getProductId().hashCode()) * -1);
        bo.setActivityId(exchActivityBO.getActivityId());
        bo.setShowStartTime(exchActivityBO.getStartTime());
        bo.setShowEndTime(exchActivityBO.getEndTime());
        bo.setExchangeStartTime(exchActivityBO.getStartTime());
        bo.setExchangeEndTime(exchActivityBO.getEndTime());
        bo.setItemName(productDTO.getProductName());
        bo.setItemType(ItemTypeEnum.WEB_PAY_ITEM);
        bo.setItemUniqValue(productDTO.getProductId());
        bo.setPrice(productDTO.getPrice());
        bo.setStockType(StockTypeEnum.NO_LIMIT);
        bo.setStockId("webpay_" + productDTO.getProductId()); // 虚拟库存ID
        bo.setTimesLimitType(productDTO.getLimitPeriod().getTimesLimitType().getType());
        bo.setTimesLimitValue(productDTO.getLimitQuantity());
        bo.setExchLimitType(ExchLimitTypeEnum.NONE.getType());
        bo.setIsEnable(true);
        bo.setPriority(0);
        
        return bo;
    }
    
    private ExchActivityItemBO saveAndQuery(WepPayProductDTO wepPayProductDTO, ExchActivityBO exchActivityBO) {
        
        ExchActivityItem item = new ExchActivityItem();
        item.setActivityId(exchActivityBO.getActivityId());
        item.setShowStartTime(exchActivityBO.getStartTime());
        item.setShowEndTime(exchActivityBO.getEndTime());
        item.setExchStartTime(exchActivityBO.getStartTime());
        item.setExchEndTime(exchActivityBO.getEndTime());
        item.setItemName(wepPayProductDTO.getProductName());
        item.setItemType(ItemTypeEnum.WEB_PAY_ITEM.getType());
        item.setItemUniqValue(wepPayProductDTO.getProductId());
        item.setPrice(wepPayProductDTO.getPrice());
        item.setStockType(StockTypeEnum.NO_LIMIT.getStockType());
        item.setTimesLimitType(wepPayProductDTO.getLimitPeriod().getTimesLimitType().getType());
        item.setTimesLimitValue(wepPayProductDTO.getLimitQuantity());
        item.setExchLimitType(ExchLimitTypeEnum.NONE.getType());
        item.setIsEnable(true);
        item.setEnableTime(exchActivityBO.getStartTime());
        
        int result = exchActivityItemRepository.insertSelective(item);
        if (result != 1) {
            log.error("WebPayExchangeItemService save failed, item:{}", item);
            throw new BusinessException(ApiCodeEnum.SYSTEM_BUSY);
        }
        return exchActivityItemRepository.get(item.getId());
    }
}
