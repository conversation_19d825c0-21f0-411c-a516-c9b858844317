package com.biligame.activity.exchange.service.impl.limit;

import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitLog;
import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitRecord;
import com.biligame.activity.exchange.dal.entity.TimesLimitRecordQuery;
import com.biligame.activity.exchange.dal.repository.ExchActivityItemRepository;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitLogRepository;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitRecordRepository;
import com.biligame.activity.exchange.exception.ExchangeException;
import com.biligame.activity.exchange.model.bo.*;
import com.biligame.activity.exchange.enums.TimesLimitType;
import com.biligame.activity.exchange.service.TimesLimitService;
import com.biligame.activity.exchange.utils.DateUtil;
import com.biligame.activity.exchange.utils.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/7
 */
@Slf4j
@Service
public class DefaultTimesLimitService implements TimesLimitService {

    @Resource
    private ExchActivityItemRepository exchActivityItemRepository;

    @Resource
    private ExchItemTimesLimitRecordRepository exchItemTimesLimitRecordRepository;

    @Resource
    private ExchItemTimesLimitLogRepository exchItemTimesLimitLogRepository;

    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    @Override
    public boolean needCheck(ExchActivityItemBO Item) {
        TimesLimitType timesLimitType = TimesLimitType.findOrDefault(Item.getTimesLimitType(), TimesLimitType.NONE);
        return timesLimitType != TimesLimitType.NONE;
    }

    @Override
    public void increase(ExchRecordBO record) throws ExchangeException {
        ExchActivityItemBO Item = exchActivityItemRepository.get(record.getItemId());
        if (!needCheck(Item)) {
            return;
        }
        String bizDate = getBizDate(Item, record.getCtime());
        
        // 使用item_uniq_value查询
        String itemUniqValue = Item.getItemUniqValue();
        ExchItemTimesLimitRecordBO recordBO =
                exchItemTimesLimitRecordRepository.findByUnifiedItemId(record.getUid(), record.getActivityId(), itemUniqValue, bizDate);

        if (record.getCount() > Item.getTimesLimitValue()) {
            throw new ExchangeException("兑换数量超过最多可兑换数量");
        }

        int exchangedTimes = (recordBO == null || recordBO.isEmpty()) ? 0 : recordBO.getTimes();

        if (record.getCount() + exchangedTimes > Item.getTimesLimitValue()) {
            // 清理缓存
            exchItemTimesLimitRecordRepository.removeCacheByUnifiedId(record.getUid(), record.getActivityId(), bizDate, itemUniqValue);
            throw new ExchangeException("兑换数量累计超过最多可兑换数量");
        }

        TransactionStatus status = dataSourceTransactionManager.getTransaction(transactionDefinition);

        try {
            if (recordBO == null || recordBO.isEmpty()) {
                // insert
                ExchItemTimesLimitRecord limitRecord = buildExchItemTimesLimitRecord(record, Item, bizDate);
                exchItemTimesLimitRecordRepository.insertSelective(limitRecord);
                ExchItemTimesLimitLog log = buildExchItemTimesLimitLog(record, limitRecord.getVersion(), limitRecord.getId(), ( byte)1);
                exchItemTimesLimitLogRepository.insertSelective(log);
            } else {
                int result = exchItemTimesLimitRecordRepository.increaseTimes(recordBO, record.getCount(), Item.getTimesLimitValue());
                if (result <= 0) {
                    throw new ExchangeException("累加个人兑换次数失败");
                }
                ExchItemTimesLimitLog log = buildExchItemTimesLimitLog(record, recordBO.getVersion() + 1, recordBO.getId(), ( byte)1);
                exchItemTimesLimitLogRepository.insertSelective(log);
            }
            dataSourceTransactionManager.commit(status);
        } catch (Exception e) {
            log.error("记录用户兑换次数异常, recordId: {}", record.getSerialNo(), e);
            dataSourceTransactionManager.rollback(status);
            throw new ExchangeException("累加个人兑换次数失败");
        } finally {
            // 清理缓存
            exchItemTimesLimitRecordRepository.removeCacheByUnifiedId(record.getUid(), record.getActivityId(), bizDate, itemUniqValue);
        }
    }

    private ExchItemTimesLimitRecord buildExchItemTimesLimitRecord(ExchRecordBO record, ExchActivityItemBO Item, String bizDate) {
        ExchItemTimesLimitRecord limitRecord = new ExchItemTimesLimitRecord();
        limitRecord.setUid(record.getUid());
        limitRecord.setActivityId(record.getActivityId());
        limitRecord.setItemId(Item.getId());
        // TODO: 待表结构更新后启用
        // limitRecord.setItemUniqValue(Item.getItemUniqValue()); // 使用item_uniq_value字段
        limitRecord.setBizDate(bizDate);
        limitRecord.setTimes(record.getCount());
        limitRecord.setVersion(0);
        return limitRecord;
    }

    private ExchItemTimesLimitLog buildExchItemTimesLimitLog(ExchRecordBO record, Integer version, Long limitRecordId, Byte type) {
        ExchItemTimesLimitLog log = new ExchItemTimesLimitLog();
        log.setUid(record.getUid());
        log.setLimitRecordId(limitRecordId);
        log.setExchRecordId(record.getSerialNo());
        log.setExchCount(record.getCount());
        log.setRecordVersion(version);
        log.setType(type);
        return log;
    }

    @Override
    public void decrease(ExchRecordBO record) throws ExchangeException {
        ExchActivityItemBO Item = exchActivityItemRepository.get(record.getItemId());
        if (!needCheck(Item)) {
            return;
        }
        String bizDate = getBizDate(Item, record.getCtime());

        ExchItemTimesLimitRecordBO recordBO =
                exchItemTimesLimitRecordRepository.findItemRecordFromDb(record.getUid(), record.getActivityId(), Item.getId(), bizDate);
        if (recordBO == null || recordBO.isEmpty()) {
            log.error("兑换记录为空, recordId: {}", record.getSerialNo());
            exchItemTimesLimitRecordRepository.removeCache(record.getUid(), record.getActivityId(), record.getBizId(), record.getItemId());
            throw new ExchangeException("兑换记录为空");
        }

        ExchItemTimesLimitLog increaseLog = exchItemTimesLimitLogRepository.selectFromMaster(record.getUid(), record.getSerialNo(), (byte)1);
        if (increaseLog == null) {
            log.info("无累加兑换记录，忽略不处理, record: {}", record.getSerialNo());
            return;
        }

        TransactionStatus status = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            int result = exchItemTimesLimitRecordRepository.decreaseTimes(recordBO, record.getCount());
            if (result <= 0) {
                throw new ExchangeException("恢复个人兑换次数失败");
            }
            ExchItemTimesLimitLog log = buildExchItemTimesLimitLog(record, recordBO.getVersion() + 1, recordBO.getId(), ( byte)2);
            exchItemTimesLimitLogRepository.insertSelective(log);

            dataSourceTransactionManager.commit(status);
        } catch (Exception e) {
            log.error("恢复用户兑换次数异常, recordId: {}", record.getSerialNo(), e);
            dataSourceTransactionManager.rollback(status);
            throw new ExchangeException("恢复个人兑换次数失败");
        } finally {
            exchItemTimesLimitRecordRepository.removeCache(record.getUid(), record.getActivityId(), bizDate, record.getItemId());
        }
    }

    @Override
    public Map<Integer, Integer> batchQuery(Long uid, String activityId, List<ExchActivityItemBO> ItemIds) {
        List<TimesLimitRecordQuery> queries = Lists.newArrayListWithCapacity(ItemIds.size());
        ItemIds.forEach(bo -> {
            String bizDate = getBizDate(bo);
            queries.add(new TimesLimitRecordQuery(bizDate, bo.getId()));
        });
        Map<Integer, ExchItemTimesLimitRecordBO> limitRecordMap =
                exchItemTimesLimitRecordRepository.findByUidAndActivityIdAndBizDate(uid, activityId, queries);

        Map<Integer, Integer> limitMap = new HashMap<>(limitRecordMap.size());
        limitRecordMap.forEach((k, v) -> limitMap.put(k, v.getTimes()));
        return limitMap;
    }

    @Override
    public Integer query(Long uid, ExchActivityItemBO Item) {
        String bizDate = getBizDate(Item);
        // 使用item_uniq_value查询
        ExchItemTimesLimitRecordBO recordBO =
                exchItemTimesLimitRecordRepository.findByUnifiedItemId(uid, Item.getActivityId(), Item.getItemUniqValue(), bizDate);
        if (recordBO == null || recordBO.isEmpty()) {
            return 0;
        }
        return recordBO.getTimes();
    }

    private String getBizDate(ExchActivityItemBO Item) {
        Date now = DateUtil.now();
        return getBizDate(Item, now);
    }

    private String getBizDate(ExchActivityItemBO Item, Date now) {

        TimesLimitType timesLimitType = TimesLimitType.findOrDefault(Item.getTimesLimitType(), TimesLimitType.NONE);
        switch (timesLimitType) {
            case DAY:
                return DateUtils.format(now, DateUtils.DAY_PATTERN2);
            case ALL:
                return "0";
            case WEEK:
                return getWeekOfYear(now);
            default:
                return "";
        }
    }

    private String getWeekOfYear(Date now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);
        int year = calendar.get(Calendar.YEAR);

        if (weekOfYear < 10) {
            return year + "0" + weekOfYear;
        }
        return year + "" + weekOfYear;
    }
}
