package com.biligame.activity.exchange.service.impl.common;

import com.alibaba.fastjson.JSON;
import com.biligame.activity.exchange.annotation.ConcurrentLimiter;
import com.biligame.activity.exchange.config.ExchConfig;
import com.biligame.activity.exchange.dal.repository.EcTaskRepository;
import com.biligame.activity.exchange.dal.repository.ExchActivityItemRepository;
import com.biligame.activity.exchange.dal.repository.ExchActivityRepository;
import com.biligame.activity.exchange.dal.repository.ExchRecordRepository;
import com.biligame.activity.exchange.dto.req.ExchangeDoRequest;
import com.biligame.activity.exchange.model.vo.ExchActivityInfoVO;
import com.biligame.activity.exchange.dto.req.ExchangeItemUserEligibilityRequest;
import com.biligame.activity.exchange.dto.req.ExchangeRecordRequest;
import com.biligame.activity.exchange.dto.resp.ExchangeItemUserEligibilityDTO;
import com.biligame.activity.exchange.dto.resp.ExchangeRecordDTO;
import com.biligame.activity.exchange.dto.resp.PageResultDTO;
import com.biligame.activity.exchange.enums.*;
import com.biligame.activity.exchange.exception.BusinessException;
import com.biligame.activity.exchange.exception.ExchangeException;
import com.biligame.activity.exchange.model.bo.*;
import com.biligame.activity.exchange.model.context.ExchActivityCtx;
import com.biligame.activity.exchange.model.context.ExchContext;
import com.biligame.activity.exchange.model.context.ExchLimitCalculateCtx;
import com.biligame.activity.exchange.model.context.ExchStockCalculateCtx;
import com.biligame.activity.exchange.model.dto.ExchLimitStatusDTO;
import com.biligame.activity.exchange.model.dto.ExchStatusDTO;
import com.biligame.activity.exchange.model.vo.*;
import com.biligame.activity.exchange.service.*;
import com.biligame.activity.exchange.service.ectask.handler.RecordIdSupport;
import com.biligame.activity.exchange.utils.ActivityStatusUtils;
import com.biligame.activity.exchange.utils.DateUtil;
import com.biligame.activity.exchange.utils.SnowflakeIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import com.biligame.activity.exchange.factory.ExchangeServiceFactory;
import com.biligame.activity.exchange.factory.ExchangeServiceFactoryManager;

/**
 * <AUTHOR>
 * @date 2022/5/26 上午8:55
 */
@Service
@Slf4j
public class ExchangeServiceImpl implements ExchangeService {

    @Resource
    private ExchActivityRepository exchActivityRepository;
    @Resource
    private ExchActivityService exchActivityService;
    @Resource
    private ExchRecordRepository exchRecordRepository;
    @Resource
    private ExchActivityItemRepository exchActivityItemRepository;
    @Resource
    private ThreadPoolTaskExecutor ioExecutor;
    @Resource
    private SnowflakeIdUtil snowflakeIdUtil;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;
    @Resource
    private ExchDelayMsgService exchDelayMsgService;
    @Resource
    private TimesLimitService timesLimitService;
    @Resource
    private WhiteGoodsComponent whiteGoodsComponent;
    @Resource
    private EcTaskRepository ecTaskRepository;
    @Resource
    private ExchangeServiceContainer exchangeServiceContainer;
    
    @Resource
    private ExchangeServiceFactoryManager exchangeServiceFactoryManager;

    @Resource
    private ExchConfig exchConfig;

    /**
     * 执行兑换操作
     *
     * @param req 兑换请求
     * @return 兑换结果
     */
    @Override
    @ConcurrentLimiter(key = "#method + #req.uid", leaseTime = 5)
    public ExchLogDetailVO doExchange(ExchangeDoRequest req) {
        //#1 获取活动信息
        ExchActivityBO exchActivityBO = exchActivityRepository.get(req.getActivityId());
        if (exchActivityBO == null) {
            throw new BusinessException(ApiCodeEnum.CODE_ACTIVITY_NOT_FUND);
        }
        
        //#2 获取对应的工厂
        ExchangeServiceFactory factory = exchangeServiceFactoryManager.getFactory(exchActivityBO.getType());
        
        //#3 获取兑换物信息
        ExchangeItemService exchangeItemService = factory.createExchangeItemService();
        String effectiveItemId = req.getEffectiveItemId();
        
        ExchActivityItemBO exchActivityItemBO = exchangeItemService.findByActivityIdAndUniqueId(exchActivityBO.getActivityId(), effectiveItemId);
                
        if (null == exchActivityItemBO) {
            throw ApiCodeEnum.CODE_ITEM_NOT_EXIST.createException();
        }
        
        // 命中白名单逻辑
        if (!whiteGoodsComponent.isAllowed(req.getUid(), effectiveItemId, exchActivityItemBO.getItemType())) {
            throw ApiCodeEnum.CODE_ITEM_NOT_EXIST.createException();
        }
        //#3 check 兑换物跟活动是否匹配
        if (!exchActivityItemBO.getActivityId().equals(exchActivityBO.getActivityId())) {
            log.warn("兑换物与活动id不匹配");
            throw ApiCodeEnum.CODE_REQ_ERROR.createException("item not exist");
        }
        //#4 check 幂等
        ExchRecordBO exchRecordBO = exchRecordRepository.selectByUk(req.getActivityId(), req.getUid(), req.getBizId());
        if (exchRecordBO != null) {
            //#5兑换记录已经存在
            return parseExchRecord(exchRecordBO);
        }
        //#6 check活动和商品信息
        checkActivity(exchActivityBO);
        // 校验是否在兑换时间中
        checkActivityOpenTime(exchActivityBO);
        checkItem(exchActivityItemBO);

        // 从工厂获取各种服务
        AssetsService assetsService = factory.createAssetsService();
        if (assetsService == null) {
            log.error("assetsService not exist from factory. exchActivityBO type:{}", exchActivityBO.getType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        
        // 现在工厂会处理默认逻辑，不需要再判断null
        StockService stockService = factory.createStockService(exchActivityItemBO);
        if (stockService == null) {
            log.error("stockService not exist. stockType:{}", exchActivityItemBO.getStockType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        
        // 现在工厂会处理默认逻辑
        ExchLimitService exchLimitService = factory.createExchLimitService(exchActivityItemBO);
        if (exchLimitService == null) {
            log.error("exchLimitService is not exist. exchLimitType:{}", exchActivityItemBO.getExchLimitType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        
        // 限购服务
        TimesLimitService timesLimitService = factory.createTimesLimitService(exchActivityItemBO);
        if (timesLimitService == null) {
            timesLimitService = this.timesLimitService;
        }
        
        ExchContext ctx = new ExchContext();
        ctx.setAssetsService(assetsService);
        ctx.setStockService(stockService);
        ctx.setActivityBO(exchActivityBO);
        ctx.setItemBO(exchActivityItemBO);
        ctx.setDoExchReqVO(req);
        ctx.setExchLimitService(exchLimitService);
        ctx.setTimesLimitService(timesLimitService);

        //#7 兑换前置check 资产检查 & 库存检查
        beforeExchangeCheck(ctx);
        //#8 开始插入兑换记录
        ExchRecordBO recordBO = createExchRecord(req, exchActivityBO, exchActivityItemBO);
        //#9 发送延迟check消息
        exchDelayMsgService.asyncSendCheckExchRecordMsg(recordBO);
        //#10 开启分布式事务 分别去扣库存和冻结资产
        startExchange(assetsService, stockService, recordBO);
        //#11 组装返回数据
        return buildExchLogDetailVO(recordBO);
    }
    
    @Override
    public List<ExchangeItemUserEligibilityDTO> checkUserEligibility(ExchangeItemUserEligibilityRequest request) {
        // 活动查询
        String activityId = request.getActivity_id();
        ExchActivityBO exchActivityBO = exchActivityRepository.get(activityId);
        if (StringUtils.isBlank(activityId) || exchActivityBO == null) {
            throw new BusinessException(ApiCodeEnum.CODE_ACTIVITY_NOT_FUND);
        }
        
        // 兑换物(商品)查询
        List<Integer> itemIds = request.getItem_ids();

        List<ExchangeItemUserEligibilityDTO> result = itemIds.stream()
                .map(itemId -> checkUserEligibility(request.getUid(), itemId, exchActivityBO))
                .collect(Collectors.toList());
        log.info("checkUserEligibility result:{}", result);
        return result;
    }
    
    private ExchangeItemUserEligibilityDTO checkUserEligibility(Long uid, Integer itemId, ExchActivityBO exchActivityBO) {
        String activityId = exchActivityBO.getActivityId();
        ExchActivityItemBO exchActivityItem = exchActivityItemRepository.get(itemId);
        if (exchActivityItem == null) {
            log.error("checkUserEligibility itemId:{} not found, activity info:{}", itemId, exchActivityBO);
            return new ExchangeItemUserEligibilityDTO(itemId, false, 0);
        }
        
        AssetsService assetsService = exchangeServiceContainer.getAssetsService(exchActivityBO.getType());
        if (assetsService == null) {
            log.error("assetsService is not exist. activity type:{}", exchActivityBO.getType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        
        StockService stockService = exchangeServiceContainer.getStockService(exchActivityItem.getStockType());
        if (stockService == null) {
            log.error("stockService not exist. stockType:{}", exchActivityItem.getStockType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        // 初始化门槛服务
        ExchLimitService exchLimitService = exchangeServiceContainer.getExchLimitService(exchActivityItem.getExchLimitTypeEnum());
        if (exchLimitService == null) {
            log.error("exchLimitService is not exist. exchLimitType:{}", exchActivityItem.getExchLimitType());
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
        ExchangeDoRequest req = new ExchangeDoRequest();
        req.setUid(uid);
        req.setActivityId(activityId);
        req.setBizId(UUID.randomUUID().toString().replace("-", ""));
        ExchContext ctx = ExchContext.builder()
                .assetsService(assetsService)
                .stockService(stockService)
                .activityBO(exchActivityBO)
                .itemBO(exchActivityItem)
                .doExchReqVO(req)
                .exchLimitService(exchLimitService)
                .timesLimitService(timesLimitService)
                .build();
        
        boolean available = true;
        try {
            beforeExchangeCheck(ctx);
        } catch (BusinessException e) {
            log.info("checkUserEligibility productId:{} not available, activity info:{}", itemId, exchActivityBO, e);
            available = false;
        } catch (Exception e) {
            log.error("checkUserEligibility productId:{} failed, activity info:{}", itemId, exchActivityBO, e);
            throw new BusinessException(e);
        }
        Integer quantity = timesLimitService.query(req.getUid(), exchActivityItem);
        return new ExchangeItemUserEligibilityDTO(itemId,available,quantity);
    }

    private ExchLogDetailVO buildExchLogDetailVO(ExchRecordBO recordBO) {
        ExchLogDetailVO resp = new ExchLogDetailVO();
        BeanUtils.copyProperties(recordBO, resp);
        if (recordBO.getExt() == null) {
            resp.setExt("");
        }
        return resp;
    }

    /**
     * 开始执行兑换逻辑
     *
     * @param assetsService 资产服务
     * @param stockService  库存服务
     * @param recordBO      兑换记录
     */
    private void startExchange(AssetsService assetsService, StockService stockService,
                               ExchRecordBO recordBO) {
        CountDownLatch countDownLatch = new CountDownLatch(3);
        ExchStatusDTO status = new ExchStatusDTO();
        asyncDoFreeze(assetsService, recordBO, status, countDownLatch);
        asyncDoDeduct(stockService, recordBO, status, countDownLatch);
        asyncDoIncreaseLimit(timesLimitService, recordBO, status, countDownLatch);
        try {
            boolean isOk = countDownLatch.await(exchConfig.getExecTimeout(), TimeUnit.MILLISECONDS);
            if (isOk) {
                if (TaskStatusEnum.FAIL.equals(status.getTimesLimitStatus())) {
                    throw ApiCodeEnum.EXCHANGE_TIMES_LIMIT_FAIL.createException();
                }
                if (TaskStatusEnum.FAIL.equals(status.getFreezeStatus())) {
                    throw ApiCodeEnum.EXCHANGE_ASSET_FREEZE_FAIL.createException();
                }
                if (TaskStatusEnum.FAIL.equals(status.getStockStatus())) {
                    throw ApiCodeEnum.EXCHANGE_STOCK_DEDUCT_FAIL.createException();
                }

            } else {
                log.info("no:{} do exchange timeout", recordBO.getSerialNo());
                throw ApiCodeEnum.EXCHANGE_FAILED.createException("进阶操作超时，进阶失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("no:{} exchange wait failed.", recordBO.getSerialNo(), e);
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        } finally {
            if (
                    TaskStatusEnum.SUCCESS.equals(status.getFreezeStatus())
                            && TaskStatusEnum.SUCCESS.equals(status.getStockStatus())
                            && TaskStatusEnum.SUCCESS.equals(status.getTimesLimitStatus())
            ) {
                int exchangeCtime = (int) (recordBO.getCtime().getTime() / 1000L);
                recordBO.setExchangeTime(exchangeCtime);
                asyncDoExchSuccess(recordBO);
            } else {
                asyncDoExchFail(recordBO);
            }
        }
    }

    private void asyncDoDeduct(StockService stockService, ExchRecordBO recordBO, ExchStatusDTO status, CountDownLatch countDownLatch) {
        ioExecutor.submit(() -> {
            try {
                stockService.deduct(recordBO);
                status.setStockStatus(TaskStatusEnum.SUCCESS);
            } catch (ExchangeException e) {
                status.setStockStatus(TaskStatusEnum.FAIL);
                log.warn("{} stockService.deduct failed. {}", recordBO.getSerialNo(), e.getMessage(), e);
            } catch (Exception e) {
                status.setStockStatus(TaskStatusEnum.FAIL);
                log.error("{} stockService.deduct failed.", recordBO.getSerialNo(), e);
            } finally {
                countDownLatch.countDown();
            }
        });

    }

    private void asyncDoIncreaseLimit(TimesLimitService timesLimitService, ExchRecordBO recordBO, ExchStatusDTO status, CountDownLatch countDownLatch) {
        ioExecutor.submit(() -> {
            try {
                timesLimitService.increase(recordBO);
                status.setTimesLimitStatus(TaskStatusEnum.SUCCESS);
            } catch (ExchangeException e) {
                status.setTimesLimitStatus(TaskStatusEnum.FAIL);
                log.warn("{} timesLimitService.increase failed. {}", recordBO.getSerialNo(), e.getMessage(), e);
            } catch (Exception e) {
                status.setTimesLimitStatus(TaskStatusEnum.FAIL);
                log.error("{} timesLimitService.increase failed.", recordBO.getSerialNo(), e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    private void asyncDoFreeze(AssetsService assetsService, ExchRecordBO recordBO, ExchStatusDTO status, CountDownLatch countDownLatch) {
        ioExecutor.submit(() -> {
            try {
                assetsService.freeze(recordBO);
                status.setFreezeStatus(TaskStatusEnum.SUCCESS);
            } catch (ExchangeException e) {
                status.setFreezeStatus(TaskStatusEnum.FAIL);
                log.warn("{} assetsService.freeze failed. {}", recordBO.getSerialNo(), e.getMessage(), e);
            } catch (Exception e) {
                status.setFreezeStatus(TaskStatusEnum.FAIL);
                log.error("{} assetsService.freeze failed.", recordBO.getSerialNo(), e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    private void asyncDoExchSuccess(ExchRecordBO recordBO) {
        ioExecutor.submit(() -> doExchSuccess(recordBO));
    }

    /**
     * 异步执行兑换失败的DB事务
     *
     * @param recordBO 兑换日志
     */
    @Override
    public void asyncDoExchFail(ExchRecordBO recordBO) {
        ioExecutor.submit(() -> doExchFail(recordBO));
    }
    
    @Override
    public PageResultDTO<ExchangeRecordDTO> queryExchangeRecords(ExchangeRecordRequest request) {
        PageInfo<ExchRecordBO> exchangeRecordResult = exchRecordRepository.findByUidAndActivityIdsAndPage(request.getUid(),
                request.getActivity_ids(), request.getPage_num(), request.getPage_size());
        if (exchangeRecordResult == null || exchangeRecordResult.getPageSize() <= 0) {
            return PageResultDTO.emptyResult(request.getPage_size(), request.getPage_num());
        }
        List<ExchangeRecordDTO> exchangeRecordDTOList = exchangeRecordResult.getList().stream()
                .map(e -> {
                    ExchangeRecordDTO exchangeRecordDTO = new ExchangeRecordDTO();
                    exchangeRecordDTO.setExchangeTime(Long.valueOf(e.getExchangeTime()));
                    exchangeRecordDTO.setCount(e.getCount());
                    exchangeRecordDTO.setPrice(Long.valueOf(e.getPrice()));
                    exchangeRecordDTO.setItemName(e.getItemTitle());
                    return exchangeRecordDTO;
                })
                .collect(Collectors.toList());
        return PageResultDTO.<ExchangeRecordDTO>builder()
                .pageNum(request.getPage_num())
                .pageSize(request.getPage_size())
                .total(exchangeRecordResult.getTotal())
                .data(exchangeRecordDTOList)
                .build();
    }
    
    /**
     * 构建checkTask
     *
     * @param recordBO          兑换记录
     * @param checkTaskTypeEnum xx
     * @return xx
     */
    private EcTaskLogBO buildEcTaskLog(ExchRecordBO recordBO, EcTaskBzTypeEnum checkTaskTypeEnum) {
        EcTaskLogBO log = new EcTaskLogBO();
        log.setBzType(checkTaskTypeEnum);
        log.setBzId(RecordIdSupport.buildBzId(recordBO.getUid(), recordBO.getId()));

        return log;
    }

    /**
     * 更改兑换记录状态为成功
     *
     * @param recordBO bo
     */
    private void doExchSuccess(ExchRecordBO recordBO) {
        TransactionStatus status = dataSourceTransactionManager.getTransaction(transactionDefinition);
        List<EcTaskLogBO> taskLogs = null;
        try {
            boolean ret = exchRecordRepository.updateRecordStatus(recordBO.getId(), recordBO.getUid(), ExchRecordStatusEnum.SUCCESS, recordBO.getExchangeTime());
            if (!ret) {
                throw ApiCodeEnum.SYSTEM_BUSY.createException("exchRecordRepository.updateRecordStatus return false");
            }
            // 确认扣除资产
            EcTaskLogBO log1 = buildEcTaskLog(recordBO, EcTaskBzTypeEnum.EXCH_ASSET_CONFIRM);
            // 确认发放兑换物
            EcTaskLogBO log2 = buildEcTaskLog(recordBO, EcTaskBzTypeEnum.EXCH_GOODS_RELEASE);

            taskLogs = Arrays.asList(log1, log2);
            ecTaskRepository.batchInsert(taskLogs);

            dataSourceTransactionManager.commit(status);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(status);
            log.error("doExchSuccess failed. no:{}", recordBO.getSerialNo(), e);
            return;
        }
        
        // 构建兑换记录缓存
        exchRecordRepository.buildCache(recordBO);
    }

    /**
     * 执行 兑换失败
     *
     * @param recordBO 兑换记录
     */
    private void doExchFail(ExchRecordBO recordBO) {
        TransactionStatus status = dataSourceTransactionManager.getTransaction(transactionDefinition);
        List<EcTaskLogBO> taskLogs = null;
        try {
            boolean ret = exchRecordRepository.updateRecordStatus(recordBO.getId(), recordBO.getUid(), ExchRecordStatusEnum.CANCEL, null);
            if (!ret) {
                throw ApiCodeEnum.SYSTEM_BUSY.createException("exchRecordRepository.updateRecordStatus return false");
            }
            // 解冻资产
            EcTaskLogBO log1 = buildEcTaskLog(recordBO, EcTaskBzTypeEnum.EXCH_ASSET_UNFREEZE);
            // 归还库存
            EcTaskLogBO log2 = buildEcTaskLog(recordBO, EcTaskBzTypeEnum.EXCH_STOCK_REVERSE);
            // 恢复兑换次数
            EcTaskLogBO log3 = buildEcTaskLog(recordBO, EcTaskBzTypeEnum.EXCH_TIMES_LIMIT_DECREASE);

            taskLogs = Arrays.asList(log1, log2, log3);
            ecTaskRepository.batchInsert(taskLogs);

            dataSourceTransactionManager.commit(status);
        } catch (Exception e) {
            dataSourceTransactionManager.rollback(status);
            log.error("doExchFail failed. no:{}", recordBO.getSerialNo(), e);
            return;
        }
    }

    /**
     * 创建兑换记录
     *
     * @param req      xx
     * @param activity xx
     * @param item    xx
     * @return xx
     */
    private ExchRecordBO createExchRecord(ExchangeDoRequest req, ExchActivityBO activity, ExchActivityItemBO item) {
        ExchRecordBO exchRecord = new ExchRecordBO();
        exchRecord.setUid(req.getUid());
        exchRecord.setActivityId(activity.getActivityId());
        exchRecord.setType(activity.getType().getType());
        exchRecord.setItemId(item.getId());
        exchRecord.setItemTitle(item.getItemName());
        exchRecord.setItemImg(item.getItemImg());
        exchRecord.setItemDesc(item.getItemDesc());
        exchRecord.setBizId(req.getBizId());
        exchRecord.setCount(req.getCount());
        exchRecord.setChannelId(activity.getChannel());
        if (req.getExtraData() != null) {
            exchRecord.setExt(JSON.toJSONString(req.getExtraData()));
        }
        exchRecord.setItemType(item.getItemType().getType());
        exchRecord.setItemUniqValue(item.getItemUniqValue());
        exchRecord.setPrice(item.getPrice());
        exchRecord.setStatus(ExchRecordStatusEnum.DOING);
        exchRecord.setSerialNo(String.format("%d", snowflakeIdUtil.nextId()));
        Date nowDate = DateUtil.now();
        exchRecord.setCtime(nowDate);
        exchRecord.setMtime(nowDate);

        exchRecordRepository.create(exchRecord);

        return exchRecord;
    }

    /**
     * 兑换前的资产和库存检查
     *
     * @param ctx 兑换上下文
     */
    private void beforeExchangeCheck(ExchContext ctx) {
        int processNum = 0;

        // 库存是否满足
        AtomicBoolean stockIsEnough = new AtomicBoolean(!ctx.getStockService().needCheckStock());
        // 兑换资产是否满足
        AtomicBoolean assetsIsEnough = new AtomicBoolean(!ctx.getAssetsService().needCheckBalance());
        // 兑换件数限制是否满足
        AtomicBoolean timeLimitEnough = new AtomicBoolean(!ctx.getTimesLimitService().needCheck(ctx.getItemBO()));
        // 门槛是否满足
        AtomicBoolean exchLimitEnough = new AtomicBoolean(!ctx.getExchLimitService().needCheck());
        if (ctx.getAssetsService().needCheckBalance()) {
            processNum++;
        }
        if (ctx.getStockService().needCheckStock()) {
            processNum++;
        }
        if (ctx.getTimesLimitService().needCheck(ctx.getItemBO())) {
            processNum++;
        }
        if (ctx.getExchLimitService().needCheck()) {
            processNum++;
        }
        if (processNum <= 0) {
            // 不需要检查资产余额 & 库存 & 购买件数限制 & 兑换限制
            // 直接返回
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(processNum);

        asyncCheckStock(ctx, stockIsEnough, countDownLatch);
        asyncCheckAssets(ctx, assetsIsEnough, countDownLatch);
        asyncCheckTimesLimit(ctx, timeLimitEnough, countDownLatch);
        asyncCheckExchLimit(ctx, exchLimitEnough, countDownLatch);

        waitExchangeCheck(stockIsEnough,
                assetsIsEnough,
                timeLimitEnough,
                exchLimitEnough,
                countDownLatch,
                ctx.getDoExchReqVO().getBizId());
    }

    private void asyncCheckExchLimit(ExchContext ctx, AtomicBoolean exchLimitEnough, CountDownLatch countDownLatch) {
        //不需要校验
        if (!ctx.getExchLimitService().needCheck()) {
            return;
        }
        ioExecutor.submit(() -> {
            try {
                boolean isLimit = ctx.getExchLimitService().isLimit(ctx.getDoExchReqVO().getUid(), ctx.getItemBO(), ctx.getActivityBO());
                exchLimitEnough.set(!isLimit);
            } catch (Exception e) {
                log.error("执行兑换门槛判断失败. uid:{} goodsId:{}",
                        ctx.getDoExchReqVO().getUid(), ctx.getItemBO().getId(), e);
                exchLimitEnough.set(false);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    private void asyncCheckStock(ExchContext ctx, AtomicBoolean stockIsEnough, CountDownLatch countDownLatch) {
        if (!ctx.getStockService().needCheckStock()) {
            return;
        }
        ioExecutor.submit(() -> {
            try {
                Integer nowStock = ctx.getStockService().query(ctx.getItemBO().getStockId());
                stockIsEnough.set(nowStock == null || nowStock >= ctx.getDoExchReqVO().getCount());
                log.info("actual stock:{} ask stock:{}", nowStock, ctx.getDoExchReqVO().getCount());
            } catch (Exception e) {
                log.info(" stockService.query failed. bzId:{}", ctx.getDoExchReqVO().getBizId(), e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    private void asyncCheckAssets(ExchContext ctx, AtomicBoolean assetsIsEnough, CountDownLatch countDownLatch) {
        if (!ctx.getAssetsService().needCheckBalance()) {
            return;
        }
        ioExecutor.submit(() -> {
            try {
                boolean isOk = ctx.getAssetsService().checkBalance(ctx.getDoExchReqVO().getActivityId(),
                        ctx.getDoExchReqVO().getUid(),
                        ctx.getItemBO().getPrice(),
                        ctx.getDoExchReqVO().getExtraData());
                assetsIsEnough.set(isOk);
            } catch (Exception e) {
                log.info("assetsService.checkBalance failed. bzId:{}", ctx.getDoExchReqVO().getBizId(), e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    private void asyncCheckTimesLimit(ExchContext ctx, AtomicBoolean timesLimitEnough, CountDownLatch countDownLatch) {
        ExchActivityItemBO goods = ctx.getItemBO();
        if (!ctx.getTimesLimitService().needCheck(goods)) {
            return;
        }
        ioExecutor.submit(() -> {
            try {
                Integer times = timesLimitService.query(ctx.getDoExchReqVO().getUid(), goods);
                timesLimitEnough.set(times < goods.getTimesLimitValue());
                log.info("actual user times:{}, limitTimes: {}", times, goods.getTimesLimitValue());
            } catch (Exception e) {
                log.info(" timesLimitService.query failed. bzId:{}", ctx.getDoExchReqVO().getBizId(), e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }

    /**
     * 等待check
     *
     * @param stockIsEnough  库存状态
     * @param assetsIsEnough 资产状态
     * @param countDownLatch cc
     * @param bzId           业务幂等id
     */
    private void waitExchangeCheck(AtomicBoolean stockIsEnough,
                                   AtomicBoolean assetsIsEnough,
                                   AtomicBoolean timeLimitEnough,
                                   AtomicBoolean exchLimitEnough,
                                   CountDownLatch countDownLatch,
                                   String bzId) {
        try {
            boolean isOk = countDownLatch.await(exchConfig.getQueryTimeout(), TimeUnit.MILLISECONDS);
            if (isOk) {
                if (!timeLimitEnough.get()) {
                    throw ApiCodeEnum.EXCHANGE_OUT_OF_LIMIT.createException();
                }
                if (!stockIsEnough.get()) {
                    throw ApiCodeEnum.EXCHANGE_STOCK_NOT_ENOUGH.createException();
                }
                if (!assetsIsEnough.get()) {
                    throw ApiCodeEnum.EXCHANGE_ASSET_NOT_ENOUGH.createException();
                }
            } else {
                // 如果校验超时 则认为校验通过 继续执行下去
                log.info("库存校验，资产校验超时. bzId:{}", bzId);
            }
            // 兑换门槛校验超时 则认为没有满足
            if (!exchLimitEnough.get()) {
                throw ApiCodeEnum.EXCHANGE_EXCH_LIMIT.createException();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("bzId:{} countDownLatch.await failed.", bzId, e);
            throw ApiCodeEnum.SYSTEM_BUSY.createException();
        }
    }

    /**
     * 解析已经存在的兑换记录
     *
     * @param recordBO 兑换记录
     * @return 兑换结果
     */
    private ExchLogDetailVO parseExchRecord(ExchRecordBO recordBO) {
        switch (recordBO.getStatus()) {
            case DOING:
                throw ApiCodeEnum.EXCHANGE_ING.createException();
            case SUCCESS:
                ExchLogDetailVO resp = new ExchLogDetailVO();
                BeanUtils.copyProperties(recordBO, resp);
                return resp;
            case CANCEL:
            default:
                throw ApiCodeEnum.EXCHANGE_CANCEL.createException();
        }
    }

    /**
     * 校验活动状态
     *
     * @param config 活动配置
     */
    private void checkActivity(ExchActivityBO config) {
        if (config == null) {
            throw ApiCodeEnum.CODE_ACTIVITY_NOT_FUND.createException();
        }
        if (!ActivityStatusEnum.PUBLISHED.equals(config.getStatus())) {
            throw ApiCodeEnum.CODE_ACTIVITY_HAS_ENDED.createException();
        }
        ActivityTimeStatusEnum activityTimeStatusEnum = ActivityStatusUtils.getActivityStatus(config.getStartTime(), config.getEndTime());
        switch (activityTimeStatusEnum) {
            case WAIT:
                throw ApiCodeEnum.CODE_ACTIVITY_NOT_START.createException();
            case ENDED:
                throw ApiCodeEnum.CODE_ACTIVITY_HAS_ENDED.createException();
            case GOING:
            default:
                //ignore
        }

    }

    private void checkActivityOpenTime(ExchActivityBO config) {
        ExchActivityExtInterface ext = config.getExt();
        if (ext == null) {
            return;
        }
        ExchOpenConfig openConfig = config.getExt().getOpenConfig();
        if (openConfig == null) {
            return;
        }
        ActivityTimeStatusEnum statusEnum = getActivityOpeningStatus(openConfig);
        if (statusEnum != ActivityTimeStatusEnum.GOING) {
            throw ApiCodeEnum.EXCHANGE_NOT_OPEN.createException();
        }
    }

    private ActivityTimeStatusEnum getActivityOpeningStatus(ExchOpenConfig openConfig) {
        Integer exchangeStartTime;
        Integer exchangeEndTime;
        if (openConfig.getType() == ExchOpenTypeEnum.ABSOLUTE.getType()) {
            exchangeStartTime = openConfig.getStartTime();
            exchangeEndTime = openConfig.getEndTime();
        } else {
            Date dateBegin = DateUtil.getBeginOfDay(null);
            Integer beginTime = (int) (dateBegin.getTime() / 1000);
            exchangeStartTime = beginTime + openConfig.getStartTime();
            exchangeEndTime = beginTime + openConfig.getEndTime();
        }
        return ActivityStatusUtils.getActivityStatus(exchangeStartTime, exchangeEndTime);
    }

    /**
     * 获取活动配置并check
     *
     * @param activityId 活动id
     * @return 活动配置对象
     */
    private ExchActivityBO getActivityWithCheck(String activityId) {
        ExchActivityBO config = exchActivityRepository.get(activityId);
        checkActivity(config);
        return config;
    }

    /**
     * 校验商品配置
     *
     * @param item 商品信息
     */
    private void checkItem(ExchActivityItemBO item) {
        if (null == item) {
            throw ApiCodeEnum.CODE_ITEM_NOT_EXIST.createException();
        }
        if (Boolean.FALSE.equals(item.getIsEnable())) {
            throw ApiCodeEnum.CODE_ITEM_NOT_EXIST.createException();
        }
        if (item.getShowEndTime() > 0 && item.getShowStartTime() > 0) {
            //验证兑换物展示时间
            ActivityTimeStatusEnum activityStatusEnum = ActivityStatusUtils.getActivityStatus(item.getShowStartTime(), item.getShowEndTime());
            switch (activityStatusEnum) {
                case WAIT:
                    throw ApiCodeEnum.CODE_ACTIVITY_NOT_START.createException();
                case ENDED:
                    throw ApiCodeEnum.CODE_ACTIVITY_HAS_ENDED.createException();
                case GOING:
                default:
                    //ignore
            }
        }
        // 判断商品的可兑换时间
        if (item.getExchangeStartTime() > 0 && item.getExchangeEndTime() > 0) {
            ActivityTimeStatusEnum exchangeTimeStatusEnum = ActivityStatusUtils.getActivityStatus(item.getExchangeStartTime(), item.getExchangeEndTime());
            if (!ActivityTimeStatusEnum.GOING.equals(exchangeTimeStatusEnum)) {
                throw ApiCodeEnum.EXCHANGE_NOT_OPEN.createException();
            }
        }

    }


    /**
     * 查询兑换记录 - 通过活动id
     *
     * @param req 请求
     * @return 分页记录
     */
    @Override
    public PageVO<ExchLogDetailVO> queryLogsByActivityId(ExchLogsReqVO req) {
        PageInfo<ExchRecordBO> recordBOPage = exchRecordRepository.findByUidAndActivityIdsAndPage(req.getUid(), Collections.singletonList(req.getActivityId()), req.getPage(), req.getPageSize());

        List<ExchLogDetailVO> voList = recordBOPage.getList().stream().map(this::buildExchLogDetailVO).collect(Collectors.toList());

        return new PageVO<>(req.getPage(), req.getPageSize(), (int) recordBOPage.getTotal(), voList);
    }

    /**
     * 获取某个活动（某个用户）的详情
     *
     * @param activityId 活动id
     * @param uid        用户id 可选
     * @return resp
     */
    @Override
    public ExchActivityInfoVO filterActivityInfoByActivityId(String activityId, Long uid) {
        ExchActivityBO activity = getActivityWithCheck(activityId);
        
        // 获取对应的工厂
        ExchangeServiceFactory factory = exchangeServiceFactoryManager.getFactory(activity.getType());
        ExchangeItemService exchangeItemService = factory.createExchangeItemService();
        
        List<ExchActivityItemBO> goods = exchangeItemService.findByActivityId(activityId);
        
        // 过滤掉非白名单商品
        goods = goods.stream()
                .filter(g -> whiteGoodsComponent.isAllowed(uid, g.getItemUniqValue(), g.getItemType()))
                .collect(Collectors.toList());

        List<ExchLimitCalculateCtx> exchLimitCalculateCtxList = batchQueryExchLimitStatus(goods);
        List<ExchStockCalculateCtx> exchStockCalculateCtxList = batchGetStockCtxList(goods);
        ExchActivityCtx activityCtx = getActivityCtx(uid, activity, exchLimitCalculateCtxList, exchStockCalculateCtxList);

        Map<Integer, Integer> userTimesMap = queryLimitUserTimes(uid, activityId, goods);
        // 渲染VO
        ExchActivityInfoVO resp = new ExchActivityInfoVO();
        resp.setTitle(activity.getTitle());
        resp.setActivityId(activityId);
        resp.setStartTime(activity.getStartTime());
        resp.setEndTime(activity.getEndTime());
        resp.setType(activity.getType().getType());
        resp.setOpenConfig(buildExchOpenConfigVO(activity));
        List<ExchItemVO> goodsVOList = buildGoodsVOList(goods, activityCtx, userTimesMap);
        // 根据优先级排序
        goodsVOList.sort(Comparator.comparing(ExchItemVO::getPriority));
        resp.setItemList(goodsVOList);

        return resp;
    }

    private ExchActivityCtx getActivityCtx(
            Long uid,
            ExchActivityBO activityBO,
            List<ExchLimitCalculateCtx> exchLimitCalculateCtxList,
            List<ExchStockCalculateCtx> stockCalculateCtxList) {
        ExchActivityCtx activityCtx = new ExchActivityCtx();
        activityCtx.setCountDownLatch(new CountDownLatch(exchLimitCalculateCtxList.size() + stockCalculateCtxList.size()));
        activityCtx.setStockMap(new ConcurrentHashMap<>(0));
        activityCtx.setExchLimitStatusDTOMap(new ConcurrentHashMap<>(0));
        activityCtx.setUid(uid);

        for (ExchLimitCalculateCtx ctx : exchLimitCalculateCtxList) {
            ctx.setUid(uid);
            ctx.setActivity(activityBO);
            ctx.setCountDownLatch(activityCtx.getCountDownLatch());
            ctx.setStatusMap(activityCtx.getExchLimitStatusDTOMap());
            ioExecutor.submit(() -> assignExchLimitStatusMap(ctx));
        }

        for (ExchStockCalculateCtx ctx : stockCalculateCtxList) {
            ctx.setStockMap(activityCtx.getStockMap());
            ctx.setCountDownLatch(activityCtx.getCountDownLatch());
            ioExecutor.submit(() -> assignStockMap(ctx));
        }
        try {
            boolean isOk = activityCtx.getCountDownLatch().await(exchConfig.getQueryTimeout(), TimeUnit.MILLISECONDS);
            if (!isOk) {
                log.warn("getActivityCtx and get exchLimitStatus countDownLatch.await failed");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("queryStock countDownLatch.await failed.", e);
        }
        return activityCtx;
    }

    private List<ExchLimitCalculateCtx> batchQueryExchLimitStatus(List<ExchActivityItemBO> goodsList) {
        Map<ExchLimitTypeEnum, List<ExchActivityItemBO>> exchLimitGroupGoodsListMap = new EnumMap<>(ExchLimitTypeEnum.class);
        for (ExchActivityItemBO goods : goodsList) {
            List<ExchActivityItemBO> groupGoodsList = exchLimitGroupGoodsListMap.computeIfAbsent(goods.getExchLimitTypeEnum(), t -> new ArrayList<>());
            groupGoodsList.add(goods);
        }
        List<ExchLimitCalculateCtx> results = new ArrayList<>();
        for (ExchLimitTypeEnum typeEnum : ExchLimitTypeEnum.values()) {
            List<ExchActivityItemBO> groupGoodsList = exchLimitGroupGoodsListMap.get(typeEnum);
            if (CollectionUtils.isNotEmpty(groupGoodsList)) {
                ExchLimitCalculateCtx ctx = new ExchLimitCalculateCtx();
                ctx.setGoodsList(groupGoodsList);
                ctx.setExchLimitTypeEnum(typeEnum);
                results.add(ctx);
            }
        }
        return results;
    }

    private void assignExchLimitStatusMap(ExchLimitCalculateCtx ctx) {
        try {
            ExchLimitService exchLimitService = exchangeServiceContainer.getExchLimitService(ctx.getExchLimitTypeEnum());
            Map<Integer, ExchLimitStatusDTO> statusMap = exchLimitService.batchGetStatus(ctx.getUid(), ctx.getActivity(), ctx.getGoodsList());
            if (MapUtils.isNotEmpty(statusMap)) {
                ctx.getStatusMap().putAll(statusMap);
            }
        } catch (Exception e) {
            log.warn("exchLimitService.batchGetStatus failed. type:{}", ctx.getExchLimitTypeEnum(), e);
        } finally {
            ctx.getCountDownLatch().countDown();
        }
    }

    private ExchOpenConfigVO buildExchOpenConfigVO(ExchActivityBO activity) {
        ExchOpenConfigVO exchOpenConfigVO = new ExchOpenConfigVO();

        if (activity.getExt() == null || activity.getExt().getOpenConfig() == null) {
            exchOpenConfigVO.setType(OpenConfigType.ABSOLUTE);
            exchOpenConfigVO.setStartTime(activity.getStartTime());
            exchOpenConfigVO.setEndTime(activity.getEndTime());
            ActivityTimeStatusEnum statusEnum = ActivityStatusUtils.getActivityStatus(activity.getStartTime(), activity.getEndTime());
            exchOpenConfigVO.setOpening(statusEnum == ActivityTimeStatusEnum.GOING);
            return exchOpenConfigVO;
        }

        ExchOpenConfig exchOpenConfig = activity.getExt().getOpenConfig();
        exchOpenConfigVO.setType(OpenConfigType.findByType(exchOpenConfig.getType()));
        exchOpenConfigVO.setStartTime(exchOpenConfig.getStartTime());
        exchOpenConfigVO.setEndTime(exchOpenConfig.getEndTime());
        ActivityTimeStatusEnum statusEnum = getActivityOpeningStatus(exchOpenConfig);
        exchOpenConfigVO.setOpening(statusEnum == ActivityTimeStatusEnum.GOING);
        return exchOpenConfigVO;
    }

    private ExchItemVO buildGoodsVO(ExchActivityItemBO goodsBO) {
        ExchItemVO goodsVO = new ExchItemVO();
        BeanUtils.copyProperties(goodsBO, goodsVO);
        goodsVO.setId(goodsBO.getId());
        goodsVO.setItemType(goodsBO.getItemType().getType());
        TimesLimitType timesLimitType = TimesLimitType.findOrDefault(goodsBO.getTimesLimitType(), TimesLimitType.NONE);
        goodsVO.setTimesLimitType(timesLimitType);
        goodsVO.setUserExchTimes(0);
        goodsVO.setStock(0);
        goodsVO.setCanExchange(true);

        return goodsVO;
    }

    /**
     * 批量查询库存信息
     *
     * @param goods 库存分组map
     * @return map<goodsId, stock> 兑换物-> 库存
     */
    @Override
    public Map<Integer, Integer> batchQueryStock(List<ExchActivityItemBO> goods) {
        Map<Integer, Integer> stockMap = new ConcurrentHashMap<>(0);
        List<ExchStockCalculateCtx> ctxList = batchGetStockCtxList(goods);
        if (CollectionUtils.isEmpty(ctxList)) {
            return new HashMap<>(0);
        }
        CountDownLatch countDownLatch = new CountDownLatch(ctxList.size());
        for (ExchStockCalculateCtx ctx : ctxList) {
            ctx.setCountDownLatch(countDownLatch);
            ctx.setStockMap(stockMap);
            ioExecutor.submit(() -> assignStockMap(ctx));
        }

        try {
            boolean isOk = countDownLatch.await(exchConfig.getQueryTimeout(), TimeUnit.MILLISECONDS);
            log.info("queryStock countDownLatch.await result:{}", isOk);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("queryStock countDownLatch.await failed.", e);
        }
        return stockMap;
    }

    @Override
    public Map<Integer, StockBO> batchQueryStockBO(List<ExchActivityItemBO> goods) {
        Map<Integer, StockBO> stockMap = new ConcurrentHashMap<>(0);
        List<ExchStockCalculateCtx> ctxList = batchGetStockCtxList(goods);
        if (CollectionUtils.isEmpty(ctxList)) {
            return new HashMap<>(0);
        }
        CountDownLatch countDownLatch = new CountDownLatch(ctxList.size());
        for (ExchStockCalculateCtx ctx : ctxList) {
            ctx.setCountDownLatch(countDownLatch);
            ctx.setStockBOMap(stockMap);
            ioExecutor.submit(() -> assignStockBOMap(ctx));
        }

        try {
            boolean isOk = countDownLatch.await(exchConfig.getQueryTimeout(), TimeUnit.MILLISECONDS);
            log.info("queryStock countDownLatch.await result:{}", isOk);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("queryStock countDownLatch.await failed.", e);
        }
        return stockMap;
    }


    public List<ExchStockCalculateCtx> batchGetStockCtxList(List<ExchActivityItemBO> goods) {
        if (CollectionUtils.isEmpty(goods)) {
            return new ArrayList<>(0);
        }
        Map<StockTypeEnum, List<ExchActivityItemBO>> stockGroupMap = new EnumMap<>(StockTypeEnum.class);

        for (ExchActivityItemBO g : goods) {
            List<ExchActivityItemBO> tempList = stockGroupMap.computeIfAbsent(g.getStockType(), t -> new ArrayList<>());
            tempList.add(g);
        }
        List<ExchStockCalculateCtx> results = new ArrayList<>();
        for (StockTypeEnum typeEnum : StockTypeEnum.values()) {
            List<ExchActivityItemBO> itemList = stockGroupMap.get(typeEnum);
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            ExchStockCalculateCtx ctx = new ExchStockCalculateCtx();
            ctx.setStockTypeEnum(typeEnum);
            ctx.setItemList(itemList);
            results.add(ctx);
        }
        return results;
    }

    private void assignStockMap(ExchStockCalculateCtx ctx) {
        try {
            List<String> stockIdList = ctx.getItemList().stream().map(ExchActivityItemBO::getStockId).collect(Collectors.toList());
            StockService stockService = exchangeServiceContainer.getStockService(ctx.getStockTypeEnum());
            if (stockService == null) {
                log.error("stockService is not exist. stockType:{}", ctx.getStockTypeEnum());
                return;
            }
            Map<String, Integer> stockIdStockMap = stockService.batchQuery(stockIdList);
            for (ExchActivityItemBO g : ctx.getItemList()) {
                Integer stock = stockIdStockMap.get(g.getStockId());
                if (stock != null) {
                    ctx.getStockMap().put(g.getId(), stock);
                }
            }
        } catch (Exception e) {
            log.error("assignStockMap failed", e);
        } finally {
            ctx.getCountDownLatch().countDown();
        }
    }

    private void assignStockBOMap(ExchStockCalculateCtx ctx) {
        try {
            List<String> stockIdList = ctx.getItemList().stream().map(ExchActivityItemBO::getStockId).collect(Collectors.toList());
            StockService stockService = exchangeServiceContainer.getStockService(ctx.getStockTypeEnum());
            if (stockService == null) {
                log.error("stockService is not exist. stockType:{}", ctx.getStockTypeEnum());
                return;
            }
            Map<String, StockBO> stockIdStockMap = stockService.batchQueryStock(stockIdList);
            for (ExchActivityItemBO g : ctx.getItemList()) {
                StockBO stock = stockIdStockMap.get(g.getStockId());
                if (stock != null) {
                    ctx.getStockBOMap().put(g.getId(), stock);
                }
            }
        } catch (Exception e) {
            log.error("assignStockMap failed", e);
        } finally {
            ctx.getCountDownLatch().countDown();
        }
    }

    private List<ExchItemVO> buildGoodsVOList(List<ExchActivityItemBO> goodsList, ExchActivityCtx activityCtx, Map<Integer, Integer> userTimesMap) {
        List<ExchItemVO> goodsVOList = new ArrayList<>();
        for (ExchActivityItemBO g : goodsList) {

            ExchLimitStatusDTO exchLimitStatusDTO = activityCtx.getExchLimitStatusDTOMap().get(g.getId());
            if (exchLimitStatusDTO == null || Boolean.FALSE.equals(exchLimitStatusDTO.getIsShow())) {
                // 商品不满足兑换门槛&& 获取获取门槛信息失败 不予展示
                log.info("mid: {} goodsId:{} not show", activityCtx.getUid(), g.getId());
                continue;
            }
            Integer stock = activityCtx.getStockMap().get(g.getId());
            ExchItemVO goodsVO = buildGoodsVO(g);
            goodsVO.setStock(stock);
            goodsVO.setUserExchTimes(userTimesMap.getOrDefault(g.getId(), 0));
            goodsVO.setCanExchange(exchLimitStatusDTO.getCanExchange());

            goodsVOList.add(goodsVO);
        }
        return goodsVOList;
    }

    private Map<Integer, Integer> queryLimitUserTimes(Long uid, String activityId, List<ExchActivityItemBO> goods) {
        if (uid == null || uid <= 0) {
            return Collections.emptyMap();
        }

        List<ExchActivityItemBO> goodsIds = goods.stream().filter(timesLimitService::needCheck).collect(Collectors.toList());
        return timesLimitService.batchQuery(uid, activityId, goodsIds);
    }

    private Integer getLimitUserTimes(Long uid, ExchActivityItemBO goods) {
        if (uid == null || uid <= 0) {
            return 0;
        }

        if (!timesLimitService.needCheck(goods)) {
            return 0;
        }
        return timesLimitService.query(uid, goods);
    }
}
