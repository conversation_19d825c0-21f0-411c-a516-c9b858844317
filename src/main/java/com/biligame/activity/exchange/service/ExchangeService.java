package com.biligame.activity.exchange.service;


import com.biligame.activity.exchange.dto.req.ExchangeDoRequest;
import com.biligame.activity.exchange.model.vo.ExchActivityInfoVO;
import com.biligame.activity.exchange.dto.req.ExchangeItemUserEligibilityRequest;
import com.biligame.activity.exchange.dto.req.ExchangeRecordRequest;
import com.biligame.activity.exchange.dto.resp.ExchangeItemUserEligibilityDTO;
import com.biligame.activity.exchange.dto.resp.ExchangeRecordDTO;
import com.biligame.activity.exchange.dto.resp.PageResultDTO;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.model.bo.ExchRecordBO;
import com.biligame.activity.exchange.model.bo.StockBO;
import com.biligame.activity.exchange.model.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ExchangeService {

    /**
     * 执行兑换操作
     *
     * @param req 兑换请求
     * @return 兑换结果
     */
    ExchLogDetailVO doExchange(ExchangeDoRequest req);

    /**
     * 查询兑换记录 - 通过活动id
     *
     * @param req 请求
     * @return 分页记录
     */
    PageVO<ExchLogDetailVO> queryLogsByActivityId(ExchLogsReqVO req);

    

    /**
     * 获取某个活动（某个用户）的详情
     *
     * @param activityId 活动id
     * @param uid        用户id 可选
     * @return resp
     */
    ExchActivityInfoVO filterActivityInfoByActivityId(String activityId, Long uid);

    /**
     * 批量查询库存信息
     *
     * @param goods 库存分组map
     * @return map<goodsId, stock> 兑换物-> 库存
     */
    Map<Integer, Integer> batchQueryStock(List<ExchActivityItemBO> goods);

    /**
     * 批量查询库存信息
     *
     * @param goods 库存分组map
     * @return map<goodsId, stock> 兑换物-> 库存
     */
    Map<Integer, StockBO> batchQueryStockBO(List<ExchActivityItemBO> goods);
    
    List<ExchangeItemUserEligibilityDTO> checkUserEligibility(ExchangeItemUserEligibilityRequest request);
    
    /**
     * 异步执行兑换失败后的DB事务
     * @param recordBO 兑换记录
     */
    void asyncDoExchFail(ExchRecordBO recordBO);
    
    /**
     * 分页查询用户指定活动下兑换记录
     * @return
     */
    PageResultDTO<ExchangeRecordDTO> queryExchangeRecords(ExchangeRecordRequest request);

}
