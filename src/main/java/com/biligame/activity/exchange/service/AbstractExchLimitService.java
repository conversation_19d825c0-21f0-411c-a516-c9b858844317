package com.biligame.activity.exchange.service;

import com.biligame.activity.exchange.model.bo.ExchActivityBO;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.model.dto.ExchLimitStatusDTO;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 兑换门槛服务抽象基类
 * 
 * <AUTHOR>
 */
public abstract class AbstractExchLimitService implements ExchLimitService {
    // 移除了显式注册逻辑，改为容器自动发现

    @Override
    public Map<Integer, ExchLimitStatusDTO> batchGetStatus(Long uid, ExchActivityBO activityBO, List<ExchActivityItemBO> goodsList) {
        return Collections.emptyMap();
    }
}
