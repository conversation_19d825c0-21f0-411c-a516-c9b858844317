package com.biligame.activity.exchange.service.impl.limit;

import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitLog;
import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitRecord;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitLogRepository;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitRecordRepository;
import com.biligame.activity.exchange.exception.ExchangeException;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.model.bo.ExchItemTimesLimitRecordBO;
import com.biligame.activity.exchange.model.bo.ExchRecordBO;
import com.biligame.activity.exchange.remote.GameProductService;
import com.biligame.activity.exchange.service.TimesLimitService;
import com.biligame.activity.exchange.enums.TimesLimitType;
import com.biligame.activity.exchange.utils.DateUtil;
import com.biligame.activity.exchange.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 积分商城限购服务
 * 专门处理远程商品的限购逻辑，可以选择：
 * 1. 调用商品服务的限购接口（如果商品服务提供）
 * 2. 在本地维护限购记录（使用item_uniq_value）
 */
@Slf4j
@Service("webPayTimesLimitService")
public class WebPayTimesLimitService implements TimesLimitService {
    
    @Resource
    private GameProductService gameProductService;
    
    @Resource
    private ExchItemTimesLimitRecordRepository exchItemTimesLimitRecordRepository;
    
    @Resource
    private ExchItemTimesLimitLogRepository exchItemTimesLimitLogRepository;
    
    @Override
    public boolean needCheck(ExchActivityItemBO item) {
        TimesLimitType timesLimitType = TimesLimitType.findOrDefault(item.getTimesLimitType(), TimesLimitType.NONE);
        return timesLimitType != TimesLimitType.NONE;
    }
    
    @Override
    public void increase(ExchRecordBO record) throws ExchangeException {
        // 这里可以选择：
        // 方案1：调用商品服务的限购累加接口
        // gameProductService.increasePurchaseCount(record.getUid(), record.getItemUniqValue(), record.getCount());
        
        // 方案2：在本地维护限购记录（复用DefaultTimesLimitService的逻辑）
        // 由于系统尚未上线，建议先使用本地维护的方式
        log.info("WebPayTimesLimitService increase called for record: {}", record.getSerialNo());
        
        // TODO: 实现限购累加逻辑
    }
    
    @Override
    public void decrease(ExchRecordBO record) throws ExchangeException {
        // 对应的恢复逻辑
        log.info("WebPayTimesLimitService decrease called for record: {}", record.getSerialNo());
        
        // TODO: 实现限购恢复逻辑
    }
    
    @Override
    public Map<Integer, Integer> batchQuery(Long uid, String activityId, List<ExchActivityItemBO> items) {
        // 批量查询限购记录
        // TODO: 实现批量查询逻辑
        return Map.of();
    }
    
    @Override
    public Integer query(Long uid, ExchActivityItemBO item) {
        // 查询单个商品的限购记录
        // 方案1：调用商品服务查询
        // Integer times = gameProductService.queryUserPurchaseCount(uid, item.getItemUniqValue());
        
        // 方案2：从本地查询
        String bizDate = getBizDate(item);
        ExchItemTimesLimitRecordBO recordBO =
                exchItemTimesLimitRecordRepository.findByUnifiedItemId(uid, item.getActivityId(), item.getUnifiedItemId(), bizDate);
        if (recordBO == null || recordBO.isEmpty()) {
            return 0;
        }
        return recordBO.getTimes();
    }
    
    private String getBizDate(ExchActivityItemBO item) {
        Date now = DateUtil.now();
        return getBizDate(item, now);
    }
    
    private String getBizDate(ExchActivityItemBO item, Date now) {
        TimesLimitType timesLimitType = TimesLimitType.findOrDefault(item.getTimesLimitType(), TimesLimitType.NONE);
        switch (timesLimitType) {
            case DAY:
                return DateUtils.format(now, DateUtils.DAY_PATTERN2);
            case ALL:
                return "0";
            case WEEK:
                return getWeekOfYear(now);
            default:
                return "";
        }
    }
    
    private String getWeekOfYear(Date now) {
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(now);
        calendar.setFirstDayOfWeek(java.util.Calendar.MONDAY);
        int weekOfYear = calendar.get(java.util.Calendar.WEEK_OF_YEAR);
        int year = calendar.get(java.util.Calendar.YEAR);

        if (weekOfYear < 10) {
            return year + "0" + weekOfYear;
        }
        return year + "" + weekOfYear;
    }
} 