package com.biligame.activity.exchange.service.impl.limit;

import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitLog;
import com.biligame.activity.exchange.dal.entity.ExchItemTimesLimitRecord;
import com.biligame.activity.exchange.dal.entity.TimesLimitRecordQuery;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitLogRepository;
import com.biligame.activity.exchange.dal.repository.ExchItemTimesLimitRecordRepository;
import com.biligame.activity.exchange.enums.TimesLimitType;
import com.biligame.activity.exchange.exception.ExchangeException;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.model.bo.ExchItemTimesLimitRecordBO;
import com.biligame.activity.exchange.model.bo.ExchRecordBO;
import com.biligame.activity.exchange.service.TimesLimitService;
import com.biligame.activity.exchange.utils.DateUtil;
import com.biligame.activity.exchange.utils.DateUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积分商城（WEB_PAY）专用限购服务
 * 当前暂时跳过限购检查，后续根据业务需求完善
 */
@Slf4j
@Service("webPayTimesLimitService")
public class WebPayTimesLimitService implements TimesLimitService {

    @Resource
    private ExchItemTimesLimitRecordRepository exchItemTimesLimitRecordRepository;
    
    @Resource
    private ExchItemTimesLimitLogRepository exchItemTimesLimitLogRepository;

    @Override
    public boolean needCheck(ExchActivityItemBO item) {
        // 积分商城暂时不做限购检查
        return false;
    }

    @Override
    public void increase(ExchRecordBO record) throws ExchangeException {
        // 积分商城暂时不做限购记录
        log.info("WebPayTimesLimitService.increase called for record: {}, skip limit check", record.getSerialNo());
    }

    @Override
    public void decrease(ExchRecordBO record) throws ExchangeException {
        // 积分商城暂时不做限购恢复
        log.info("WebPayTimesLimitService.decrease called for record: {}, skip limit restore", record.getSerialNo());
    }

    @Override
    public Map<Integer, Integer> batchQuery(Long uid, String activityId, List<ExchActivityItemBO> items) {
        // 返回空的限购记录
        Map<Integer, Integer> result = new HashMap<>();
        for (ExchActivityItemBO item : items) {
            result.put(item.getId(), 0);
        }
        return result;
    }

    @Override
    public Integer query(Long uid, ExchActivityItemBO item) {
        // 积分商城暂时返回0（未购买）
        return 0;
    }
} 