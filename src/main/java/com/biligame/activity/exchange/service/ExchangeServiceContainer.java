package com.biligame.activity.exchange.service;

import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.enums.ExchLimitTypeEnum;
import com.biligame.activity.exchange.enums.ItemTypeEnum;
import com.biligame.activity.exchange.enums.StockTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务容器，自动发现并管理各种服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/25
 */
@Slf4j
@Component
public class ExchangeServiceContainer {

    @Autowired
    private ApplicationContext applicationContext;
    
    @Resource
    private TimesLimitService defaultTimesLimitService;

    private final Map<StockTypeEnum, StockService> stockServiceMap = new HashMap<>();
    private final Map<ActivityTypeEnum, AssetsService> assetsServiceMap = new HashMap<>();
    private final Map<ItemTypeEnum, ItemReleaseService> itemReleaseServiceMap = new HashMap<>();
    private final Map<ExchLimitTypeEnum, ExchLimitService> exchLimitServiceMap = new HashMap<>();
    private final Map<ActivityTypeEnum, ExchangeItemService> exchangeItemServiceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 自动发现并注册StockService
        Map<String, StockService> stockServices = applicationContext.getBeansOfType(StockService.class);
        for (StockService service : stockServices.values()) {
            if (service.getSupportStockType() != null) {
                stockServiceMap.put(service.getSupportStockType(), service);
                log.info("Auto-registered StockService: {} for type: {}", 
                    service.getClass().getSimpleName(), service.getSupportStockType());
            }
        }
        
        // 自动发现并注册AssetsService
        Map<String, AssetsService> assetsServices = applicationContext.getBeansOfType(AssetsService.class);
        for (AssetsService service : assetsServices.values()) {
            if (service.getSupportActivityType() != null) {
                assetsServiceMap.put(service.getSupportActivityType(), service);
                log.info("Auto-registered AssetsService: {} for type: {}", 
                    service.getClass().getSimpleName(), service.getSupportActivityType());
            }
        }
        
        // 自动发现并注册ItemReleaseService
        Map<String, ItemReleaseService> itemReleaseServices = applicationContext.getBeansOfType(ItemReleaseService.class);
        for (ItemReleaseService service : itemReleaseServices.values()) {
            if (service.getSupportType() != null) {
                itemReleaseServiceMap.put(service.getSupportType(), service);
                log.info("Auto-registered ItemReleaseService: {} for type: {}", 
                    service.getClass().getSimpleName(), service.getSupportType());
            }
        }
        
        // 自动发现并注册ExchLimitService
        Map<String, ExchLimitService> exchLimitServices = applicationContext.getBeansOfType(ExchLimitService.class);
        for (ExchLimitService service : exchLimitServices.values()) {
            if (service.getSupportType() != null) {
                exchLimitServiceMap.put(service.getSupportType(), service);
                log.info("Auto-registered ExchLimitService: {} for type: {}", 
                    service.getClass().getSimpleName(), service.getSupportType());
            }
        }
        
        // 自动发现并注册ExchangeItemService
        Map<String, ExchangeItemService> exchangeItemServices = applicationContext.getBeansOfType(ExchangeItemService.class);
        for (ExchangeItemService service : exchangeItemServices.values()) {
            if (service.getSupportType() != null) {
                exchangeItemServiceMap.put(service.getSupportType(), service);
                log.info("Auto-registered ExchangeItemService: {} for type: {}", 
                    service.getClass().getSimpleName(), service.getSupportType());
            }
        }
    }

    // 保留原有的注册方法以兼容旧代码，但标记为过时
    @Deprecated
    public void registerExchLimitService(ExchLimitTypeEnum exchLimitTypeEnum, ExchLimitService service) {
        exchLimitServiceMap.put(exchLimitTypeEnum, service);
    }

    @Deprecated
    public void registerStockService(StockTypeEnum stockTypeEnum, StockService service) {
        stockServiceMap.put(stockTypeEnum, service);
    }

    @Deprecated
    public void registerAssetsService(ActivityTypeEnum activityTypeEnum, AssetsService assetsService) {
        assetsServiceMap.put(activityTypeEnum, assetsService);
    }

    @Deprecated
    public void registerItemReleaseService(ItemTypeEnum itemTypeEnum, ItemReleaseService goodsReleaseService) {
        itemReleaseServiceMap.put(itemTypeEnum, goodsReleaseService);
    }
    
    @Deprecated
    public void registerExchangeItemService(ActivityTypeEnum activityTypeEnum, ExchangeItemService exchangeItemService) {
        exchangeItemServiceMap.put(activityTypeEnum, exchangeItemService);
    }

    public StockService getStockService(StockTypeEnum stockTypeEnum) {
        return stockServiceMap.get(stockTypeEnum);
    }

    public AssetsService getAssetsService(ActivityTypeEnum activityTypeEnum) {
        return assetsServiceMap.get(activityTypeEnum);
    }

    public ItemReleaseService getItemReleaseService(ItemTypeEnum itemTypeEnum) {
        return itemReleaseServiceMap.get(itemTypeEnum);
    }
    
    public ExchangeItemService getExchangeItemService(ActivityTypeEnum activityTypeEnum) {
        return exchangeItemServiceMap.get(activityTypeEnum);
    }

    public ExchLimitService getExchLimitService(ExchLimitTypeEnum exchLimitTypeEnum) {
        return exchLimitServiceMap.get(exchLimitTypeEnum);
    }
    
    public TimesLimitService getDefaultTimesLimitService() {
        return defaultTimesLimitService;
    }

}
