package com.biligame.activity.exchange.service.impl.item;

import com.biligame.activity.exchange.dal.repository.ExchActivityItemRepository;
import com.biligame.activity.exchange.enums.ActivityTypeEnum;
import com.biligame.activity.exchange.model.bo.ExchActivityItemBO;
import com.biligame.activity.exchange.service.ExchangeItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 通用活动的商品服务
 */
@Slf4j
@Service
public class CommonExchangeItemService implements ExchangeItemService {
    
    @Resource
    private ExchActivityItemRepository exchActivityItemRepository;
    
    @Override
    public ActivityTypeEnum getSupportType() {
        return ActivityTypeEnum.COMMON;
    }
    
    @Override
    public List<ExchActivityItemBO> findByActivityId(String activityId) {
        return exchActivityItemRepository.findByActivityId(activityId);
    }
    
    @Override
    public ExchActivityItemBO findByActivityIdAndUniqueId(String activityId, String uniqueId) {
        // 对于内部商品，先尝试按itemId查询（兼容老接口）
        try {
            Integer itemId = Integer.valueOf(uniqueId);
            ExchActivityItemBO item = exchActivityItemRepository.get(itemId);
            if (item != null && item.getActivityId().equals(activityId)) {
                return item;
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续按item_uniq_value查询
        }
        
        // 按item_uniq_value查询
        List<ExchActivityItemBO> items = exchActivityItemRepository.findByActivityId(activityId);
        return items.stream()
                .filter(item -> uniqueId.equals(item.getItemUniqValue()))
                .findFirst()
                .orElse(null);
    }
} 