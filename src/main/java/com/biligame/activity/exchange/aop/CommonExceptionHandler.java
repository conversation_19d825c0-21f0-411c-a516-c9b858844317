package com.biligame.activity.exchange.aop;


import com.biligame.activity.exchange.exception.BusinessException;
import com.biligame.activity.exchange.enums.ApiCodeEnum;
import com.biligame.activity.exchange.model.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * 统一异常处理类
 */
@Slf4j
@RestControllerAdvice
public class CommonExceptionHandler {
    
    @Resource
    private MessageSource messageSource;

    /**
     * 公共异常.
     */
    @ExceptionHandler(value = {BusinessException.class})
    public Result<Object> businessExceptionHandler(Exception e, HttpServletRequest request) {
        log.warn("handle business exception", e);
        Integer errorCode = ((BusinessException) e).getErrorCode();
        String message = messageSource.getMessage(String.valueOf(errorCode), null, LocaleContextHolder.getLocale());
        Result<Object> ret = Result.fail(errorCode, message);
        
        return ret;
    }


    /**
     * 自定义异常统一处理.
     */
    @ExceptionHandler(Exception.class)
    public Result<Object> exception(Exception e, HttpServletRequest request) {
        log.error("handle exception", e);
        Result<Object> ret = Result.fail(ApiCodeEnum.SYSTEM_BUSY);
        ;
        return ret;
    }

    /**
     * 入参校验异常捕获类
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class, BindException.class,
            MethodArgumentTypeMismatchException.class, HttpRequestMethodNotSupportedException.class,
            MissingServletRequestParameterException.class, HttpMediaTypeNotAcceptableException.class,
            HttpMediaTypeNotSupportedException.class})
    public Result<Object> apiParamValidateExceptionHandler(Exception e, HttpServletRequest request) {
        String errorMessage = "[param error]";

        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException exception = (MethodArgumentNotValidException) e;
            FieldError fieldError = exception.getBindingResult().getFieldError();
            if (fieldError != null) {
                errorMessage = String.format("[param error] %s <%s>", fieldError.getDefaultMessage(), fieldError.getField());
            }
        }

        if (e instanceof BindException) {
            BindException exception = (BindException) e;
            FieldError fieldError = exception.getFieldError();
            if (fieldError != null) {
                errorMessage = String.format("[param error] %s <%s>", fieldError.getDefaultMessage(), fieldError.getField());
            }
        }

        if (e instanceof MethodArgumentTypeMismatchException) {
            MethodArgumentTypeMismatchException exception = (MethodArgumentTypeMismatchException) e;
            errorMessage = String.format("param[%s]type error", exception.getName());
        }

        if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException exception = (MissingServletRequestParameterException) e;
            errorMessage = exception.getMessage();
        }

        if (e instanceof HttpRequestMethodNotSupportedException) {
            HttpRequestMethodNotSupportedException exception = (HttpRequestMethodNotSupportedException) e;
            errorMessage = exception.getMessage();
        }
        if (e instanceof HttpMediaTypeNotAcceptableException) {
            HttpMediaTypeNotAcceptableException exception = (HttpMediaTypeNotAcceptableException) e;
            errorMessage = exception.getMessage();
        }
        if (e instanceof HttpMediaTypeNotSupportedException) {
            HttpMediaTypeNotSupportedException exception = (HttpMediaTypeNotSupportedException) e;
            errorMessage = exception.getMessage();
        }
        log.warn(errorMessage, e);
        // 将错误的参数的详细信息封装到统一的返回实体
        Result<Object> ret = Result.fail(ApiCodeEnum.CODE_REQ_ERROR, errorMessage);

        return ret;
    }
}
